# Surabhi API – Flutter App Backend

A **production-ready, high-performance, and scalable API backend** built with FastAPI, designed to support **50,000+ concurrent users**. This enterprise-grade system incorporates modern architecture patterns, comprehensive security, and advanced performance optimizations for Flutter mobile and web applications.

## 🚀 Key Features

### **Core Technology Stack**
- **FastAPI** with async/await support for maximum performance
- **SQLModel** with PostgreSQL for robust data management
- **Redis** for high-performance caching and session management
- **Docker** containerization for consistent deployments

### **Enterprise Security**
- **JWT Authentication** with token rotation and blacklisting
- **Advanced Password Security** with bcrypt and timing attack protection
- **Role-Based Access Control (RBAC)** with granular permissions
- **Rate Limiting** with multiple algorithms (sliding window, token bucket)
- **Security Headers** and CSRF protection
- **Input Validation** with comprehensive sanitization

### **High-Performance Architecture**
- **50k+ Concurrent Users** support with optimized connection pooling
- **Multi-Level Caching** with intelligent TTL management
- **Database Optimization** with PostgreSQL-specific indexes
- **Async Task Processing** with background job management
- **Performance Monitoring** with real-time metrics

### **Developer Experience**
- **Comprehensive Documentation** with interactive Swagger UI
- **Complete Test Suite** with 35+ unit and integration tests
- **Docker Development Environment** with hot reloading
- **Structured Logging** with correlation IDs and sanitization

## 📁 Project Structure

```
surabhi_api/
├── app/                              # Main application directory
│   ├── api/                          # API layer
│   │   └── v1/endpoints/             # Version 1 API endpoints
│   ├── core/                         # Core application components
│   ├── db/                           # Database layer
│   │   ├── database.py               # Database connection & session management
│   ├── models/                       # Pydantic models for API contracts
│   ├── schemas/                      # SQLModel database schemas
│   ├── repositories/                 # Data access layer (Repository pattern)
│   ├── services/                     # Business logic layer
│   ├── utils/                        # Utility functions and helpers
│   ├── templates/                    # Email and notification templates
│   └── main.py                       # FastAPI application entry point
├── tests/                            # Comprehensive test suite
│   ├── unit/                         # Unit tests
│   ├── integration/                  # Integration tests
│   ├── performance/                  # Performance and load tests
│   └── conftest.py                   # Test configuration and fixtures
├── docker-compose.yml                # Multi-service Docker setup
├── Dockerfile                        # Production-ready container image
├── requirements.txt                  # Python dependencies
├── pytest.ini                        # Test configuration
├── .env.example                      # Environment variables template
└── README.md                         # This documentation
```

## 🚀 Quick Start

### **Option 1: Docker Compose (Recommended)**

The fastest way to get the complete system running with all dependencies.

1. **Clone and Setup:**
   ```bash
   git clone repository_url
   cd respository_name
   cp .env.example .env
   ```

2. **Start All Services:**
   ```bash
   # Start core services (PostgreSQL, Redis, API)
   docker-compose up -d postgres redis api
   ```

3. **Verify Installation:**
   ```bash
   # Check service health
   curl http://localhost:8000/health

   # View API documentation
   open http://localhost:8000/docs
   ```

### **Option 2: Local Development**

For active development with hot reloading and debugging.

1. **Prerequisites:**
   - Python 3.11+
   - PostgreSQL 15+
   - Redis 7+

2. **Setup Environment:**
   ```bash
   # Create virtual environment
   python3 -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate

   # Install dependencies
   pip install -r requirements.txt
   ```

4. **Start Development Server:**
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

## 🧪 Testing

The project includes a comprehensive test suite with 35+ tests covering all layers.

### **Run All Tests:**
```bash
# Run unit tests
pytest tests/unit/ -v

# Run integration tests
pytest tests/integration/ -v

# Run Docker integration tests (requires Docker services)
pytest tests/integration/test_docker_integration.py -v

# Run performance tests
pytest tests/performance/ -v

# Run all tests with coverage
pytest --cov=app --cov-report=html
```

### **Test Structure:**
- **Unit Tests**: Repository and service layer testing with mocks
- **Integration Tests**: Full API endpoint testing with real database
- **Performance Tests**: Load and stress testing with concurrent requests


## 🏗️ Architecture

This system follows **enterprise-grade architecture patterns**:

- **Clean Architecture** with clear separation of concerns
- **Repository Pattern** for data access abstraction
- **Dependency Injection** for testability and modularity
- **SOLID Principles** throughout the codebase
- **Microservice-Ready** design for future scaling
