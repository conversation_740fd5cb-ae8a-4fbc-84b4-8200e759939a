# Copy this file to .env and fill in the required values.

APP_NAME="Your App Name"
ENVIRONMENT="development"  # Options: development, testing, production
DEBUG=true # Set to false in production

DATABASE_URL="postgresql://user:password@localhost:5432/app_db"
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_ECHO=false

REDIS_URL="redis://localhost:6379/0"
REDIS_PASSWORD="your-redis-password"

SECRET_KEY="your-super-secret-key-change-this-in-production"   # openssl rand -hex 32
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM="HS256"

CORS_ORIGINS=http://localhost:3000,http://localhost:8000

SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST="smtp.gmail.com"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
EMAILS_FROM_EMAIL="<EMAIL>"
EMAILS_FROM_NAME="Your App Name"

LOG_LEVEL="INFO"  # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL
