"""
Base database schemas and utilities.

Provides base classes and common fields for all database schemas
using SQLModel with proper timestamps and UUID ID management.
"""

from datetime import datetime
from sqlmodel import SQLModel, Field
from sqlalchemy import Column, DateTime
from sqlalchemy.sql import func
from uuid import UUID, uuid4



class TimestampMixin(SQLModel):
    """
    Mixin class that adds timestamp fields to models.
    
    Provides created_at and updated_at fields with automatic
    timestamp management.
    """
    
    created_at: datetime = Field(
        sa_column=Column(
            DateTime(timezone=True),
            server_default=func.now(),
            nullable=False,
        ),
        description="Timestamp when the record was created"
    )

    updated_at: datetime = Field(
        sa_column=Column(
            DateTime(timezone=True),
            server_default=func.now(),
            onupdate=func.now(),
            nullable=False,
        ),
        description="Timestamp when the record was last updated"
    )


class BaseSchema(TimestampMixin):
    """
    Base Schema class for all database tables.

    Includes:
    - UUID primary key with automatic generation
    - Created and updated timestamps
    - Common model configuration

    Note: This is a base class. Inherit from this and add table=True
    to create actual database tables.
    """

    id: UUID = Field(
        default_factory=uuid4,
        primary_key=True,
        description="Unique identifier for the record"
    )

    class Config:
        """SQLModel configuration."""
        # Enable ORM mode for Pydantic serialization
        from_attributes = True
        # Use enum values instead of enum names
        use_enum_values = True
        # Validate assignment to model fields
        validate_assignment = True
        # Allow population by field name or alias
        populate_by_name = True