"""
User database schemas using SQLModel.

Defines User and Account tables with proper relationships and role system.
"""

from __future__ import annotations
from typing import Optional
from sqlmodel import Field, Relationship, SQLModel
from enum import Enum
from uuid import UUID, uuid4
from datetime import datetime, timezone




class UserRoleEnum(str, Enum):
    ADMIN = "admin"
    APPROVER = "approver"
    EMPLOYEE = "employee"
    PREACHER = "preacher"
    VOLUNTEER = "volunteer"


class User(SQLModel, table=True):
    """User table with authentication and profile information."""
    __tablename__ = "user"

    # Primary key and timestamps
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # User fields
    email: str = Field(unique=True, index=True)
    phone_number: Optional[str] = Field(unique=True, default=None)
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    image: Optional[str] = None
    role: UserRoleEnum
    hashed_password: str
    is_active: bool = Field(default=True)
    is_2fa_enabled: bool = Field(default=False)




class Account(SQLModel, table=True):
    """Account table for user session and token management."""
    __tablename__ = "account"

    # Primary key and timestamps
    id: UUID = Field(default_factory=uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

    # Account fields
    user_id: UUID = Field(foreign_key="user.id", index=True)


    # Account metadata
    account_name: Optional[str] = None
    account_type: Optional[str] = Field(default="individual")
    is_active: bool = Field(default=True)

    # Token management
    access_token: Optional[str] = None
    expires_at: Optional[datetime] = None
    refresh_token: Optional[str] = None
    refresh_token_expires_at: Optional[datetime] = None

    # Login tracking
    last_login: Optional[datetime] = None
    last_password_change: Optional[datetime] = None
    last_failed_login_attempts: int = Field(default=0)
    last_failed_login: Optional[datetime] = None


# Define relationships after both classes are defined
User.account = Relationship(back_populates="user")
Account.user = Relationship(back_populates="account")
