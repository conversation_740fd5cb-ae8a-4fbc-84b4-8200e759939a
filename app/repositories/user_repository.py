# app/repositories/user_repository.py
"""
User repository implementation with domain-specific operations.
Extends base repository with user-specific queries and operations.
"""

from typing import Optional, Dict, Any
from uuid import UUID
from sqlmodel import Session, select, func, and_
from sqlalchemy.orm import selectinload, noload
from app.repositories.base_repository import BaseRepository
from app.schemas.user_schema import User
from app.core.exceptions import handle_database_exceptions
from app.models.common_models import (
    PaginationParams,
    PaginatedResponse,
)
from app.db.query_builder import paginate_query


class IUserRepository:
    """Interface for user-specific repository operations."""

    def get_by_email(self, email: str, session: Session) -> Optional[User]:
        """Get user by email address."""
        pass

    def get_by_phone(self, phone_number: str, session: Session) -> Optional[User]:
        """Get user by phone number."""
        pass

    def get_with_account(self, user_id: UUID, session: Session) -> Optional[User]:
        """Get user with account relationship loaded."""
        pass


class UserRepository(BaseRepository[User], IUserRepository):
    """
    User repository with domain-specific operations.
    Implements both base repository interface and user-specific interface.
    """

    def __init__(self):
        super().__init__(User)

    @handle_database_exceptions
    def get_by_email(self, email: str, session: Session) -> Optional[User]:
        """
        Get user by email address.

        Args:
            email: User's email address
            session: Database session

        Returns:
            User or None if not found
        """
        from app.utils.formatters import mask_email

        query = (
            select(User).where(User.email == email).options(noload(User.account))
        )
        user = session.exec(query).first()

        if user:
            self.logger.debug(f"Found user by email: {mask_email(email)}")
        else:
            self.logger.debug(f"User not found by email: {mask_email(email)}")

        return user

    @handle_database_exceptions
    def get_by_phone(self, phone_number: str, session: Session) -> Optional[User]:
        """
        Get user by phone number.

        Args:
            phone_number: User's phone number
            session: Database session

        Returns:
            User or None if not found
        """
        from app.utils.formatters import mask_phone_number

        query = (
            select(User)
            .where(User.phone_number == phone_number)
            .options(noload(User.account))
        )
        user = session.exec(query).first()

        if user:
            self.logger.debug(f"Found user by phone: {mask_phone_number(phone_number)}")
        else:
            self.logger.debug(f"User not found by phone: {mask_phone_number(phone_number)}")

        return user

    @handle_database_exceptions
    def get_with_account(self, user_id: UUID, session: Session) -> Optional[User]:
        """
        Get user with account relationship eagerly loaded.

        Args:
            user_id: User's UUID
            session: Database session

        Returns:
            User with account or None if not found
        """
        query = (
            select(User)
            .where(User.id == user_id)
            .options(selectinload(User.account))
        )
        user = session.exec(query).first()

        if user:
            self.logger.debug(f"Found user with account: {user_id}")
        else:
            self.logger.debug(f"User with account not found: {user_id}")

        return user

    @handle_database_exceptions
    def get_by_email_with_account(
        self, email: str, session: Session
    ) -> Optional[User]:
        """
        Get user by email with account relationship eagerly loaded.

        Args:
            email: User's email address
            session: Database session

        Returns:
            User with account or None if not found
        """
        from app.utils.formatters import mask_email

        query = (
            select(User)
            .where(User.email == email)
            .options(selectinload(User.account))
        )
        user = session.exec(query).first()

        if user:
            self.logger.debug(f"Found user with account by email: {mask_email(email)}")
        else:
            self.logger.debug(f"User with account not found by email: {mask_email(email)}")

        return user

    @handle_database_exceptions
    def get_active_users(
        self, session: Session, pagination: PaginationParams
    ) -> PaginatedResponse[User]:
        """
        Get active users with pagination.

        Args:
            session: Database session
            pagination: Pagination parameters

        Returns:
            Paginated response with active users
        """
        base_query = (
            select(User).where(User.is_active).options(noload(User.account))
        )
        count_query = select(func.count(User.id)).where(User.is_active)

        result = paginate_query(session, base_query, pagination, count_query)

        self.logger.debug(
            f"Retrieved {len(result.items)} active users (page {pagination.page})"
        )
        return result

    @handle_database_exceptions
    def get_paginated(
        self,
        session: Session,
        pagination: PaginationParams,
        filters: Optional[Dict[str, Any]] = None
    ) -> PaginatedResponse[User]:
        """
        Get paginated users with optional filters.

        Args:
            session: Database session
            pagination: Pagination parameters
            filters: Optional filters to apply

        Returns:
            Paginated response with users
        """
        # Build base query
        base_query = select(User).options(noload(User.account))

        # Apply filters
        if filters:
            filter_conditions = []
            for field, value in filters.items():
                if hasattr(User, field):
                    filter_conditions.append(getattr(User, field) == value)

            if filter_conditions:
                base_query = base_query.where(and_(*filter_conditions))

        # Build count query with same filters
        count_query = select(func.count(User.id))
        if filters:
            filter_conditions = []
            for field, value in filters.items():
                if hasattr(User, field):
                    filter_conditions.append(getattr(User, field) == value)

            if filter_conditions:
                count_query = count_query.where(and_(*filter_conditions))

        result = paginate_query(session, base_query, pagination, count_query)

        self.logger.debug(
            f"Retrieved {len(result.items)} users (page {pagination.page})",
            filters=filters
        )
        return result
