"""
Base repository pattern implementation for data access layer.

Provides generic CRUD operations and common database patterns.
"""

from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Optional, List, Dict, Any, Type
from uuid import UUID

from sqlmodel import Session, SQLModel, select, func, and_
from sqlalchemy.orm import noload

from app.core.logging import get_logger
from app.core.exceptions import DatabaseError, handle_database_exceptions

T = TypeVar("T", bound=SQLModel)


class IRepository(ABC, Generic[T]):
    """Repository interface."""

    @abstractmethod
    def get_by_id(self, entity_id: UUID, session: Session) -> Optional[T]:
        """Get entity by ID."""
        pass

    @abstractmethod
    def get_all(
        self, session: Session, skip: int = 0, limit: int = 100
    ) -> List[T]:
        """Get all entities with pagination."""
        pass

    @abstractmethod
    def create(self, entity: T, session: Session) -> T:
        """Create new entity."""
        pass

    @abstractmethod
    def update(self, entity: T, session: Session) -> T:
        """Update existing entity."""
        pass

    @abstractmethod
    def delete(self, entity_id: UUID, session: Session) -> bool:
        """Delete entity by ID."""
        pass

    @abstractmethod
    def exists(self, entity_id: UUID, session: Session) -> bool:
        """Check if entity exists."""
        pass


class BaseRepository(IRepository[T], Generic[T]):
    """
    Base repository implementation providing common CRUD operations.
    Follows Single Responsibility and Open/Closed principles.
    """

    def __init__(self, model_class: Type[T]):
        self.model_class = model_class
        self.logger = get_logger(f"repository.{model_class.__name__.lower()}")

    def get_by_id(
        self, entity_id: UUID, session: Session, load_relationships: bool = False
    ) -> Optional[T]:
        """
        Get entity by ID with optional relationship loading.

        Args:
            entity_id: Entity UUID
            session: Database session
            load_relationships: Whether to load relationships

        Returns:
            Entity or None if not found
        """
        try:
            query = select(self.model_class).where(self.model_class.id == entity_id)

            if not load_relationships:
                # Explicitly don't load relationships for performance
                query = query.options(noload("*"))

            result = session.exec(query).first()

            if result:
                self.logger.debug(
                    f"Found {self.model_class.__name__} with ID: {entity_id}"
                )
            else:
                self.logger.debug(
                    f"{self.model_class.__name__} not found with ID: {entity_id}"
                )

            return result

        except Exception as e:
            self.logger.error(
                f"Error getting {self.model_class.__name__} by ID",
                entity_id=str(entity_id),
                error=str(e),
                error_type=type(e).__name__,
                exc_info=True
            )
            raise DatabaseError(f"Failed to retrieve {self.model_class.__name__}", cause=e)

    def get_all(
        self,
        session: Session,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
    ) -> List[T]:
        """
        Get all entities with filtering and pagination.

        Args:
            session: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Optional filters to apply
            order_by: Optional field to order by

        Returns:
            List of entities
        """
        try:
            query = select(self.model_class)

            # Apply filters
            if filters:
                conditions = []
                for field, value in filters.items():
                    if hasattr(self.model_class, field):
                        column = getattr(self.model_class, field)
                        if isinstance(value, list):
                            conditions.append(column.in_(value))
                        else:
                            conditions.append(column == value)

                if conditions:
                    query = query.where(and_(*conditions))

            # Apply ordering
            if order_by and hasattr(self.model_class, order_by):
                column = getattr(self.model_class, order_by)
                query = query.order_by(column)

            # Apply pagination
            query = query.offset(skip).limit(limit)

            results = session.exec(query).all()

            self.logger.debug(
                f"Retrieved {len(results)} {self.model_class.__name__} entities"
            )
            return results

        except Exception as e:
            self.logger.error(
                f"Error retrieving {self.model_class.__name__} entities",
                filters=filters,
                error=str(e),
                error_type=type(e).__name__,
                exc_info=True
            )
            raise DatabaseError(f"Failed to retrieve {self.model_class.__name__} entities", cause=e)

    @handle_database_exceptions
    def create(self, entity: T, session: Session) -> T:
        """Create new entity."""
        session.add(entity)
        session.commit()
        session.refresh(entity)

        self.logger.info(
            f"Created {self.model_class.__name__} with ID: {entity.id}"
        )
        return entity

    @handle_database_exceptions
    def update(self, entity: T, session: Session) -> T:
        """Update existing entity."""
        session.add(entity)
        session.commit()
        session.refresh(entity)

        self.logger.info(
            f"Updated {self.model_class.__name__} with ID: {entity.id}"
        )
        return entity

    @handle_database_exceptions
    def delete(self, entity_id: UUID, session: Session) -> bool:
        """
        Delete entity by ID.

        Args:
            entity_id: Entity UUID
            session: Database session

        Returns:
            True if deleted, False if not found
        """
        entity = self.get_by_id(entity_id, session)
        if not entity:
            return False

        session.delete(entity)
        session.commit()

        self.logger.info(
            f"Deleted {self.model_class.__name__} with ID: {entity_id}"
        )
        return True

    def exists(self, entity_id: UUID, session: Session) -> bool:
        """
        Check if entity exists.

        Args:
            entity_id: Entity UUID
            session: Database session

        Returns:
            True if exists, False otherwise
        """
        try:
            query = select(func.count(self.model_class.id)).where(
                self.model_class.id == entity_id
            )
            count = session.exec(query).one()
            return count > 0

        except Exception as e:
            self.logger.error(
                f"Error checking existence of {self.model_class.__name__} with ID {entity_id}: {e}"
            )
            raise

    def count(
        self, session: Session, filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        Count entities with optional filters.

        Args:
            session: Database session
            filters: Optional filters to apply

        Returns:
            Count of entities
        """
        try:
            query = select(func.count(self.model_class.id))

            # Apply filters
            if filters:
                conditions = []
                for field, value in filters.items():
                    if hasattr(self.model_class, field):
                        column = getattr(self.model_class, field)
                        if isinstance(value, list):
                            conditions.append(column.in_(value))
                        else:
                            conditions.append(column == value)

                if conditions:
                    query = query.where(and_(*conditions))

            count = session.exec(query).one()
            return count

        except Exception as e:
            self.logger.error(
                f"Error counting {self.model_class.__name__}",
                filters=filters,
                error=str(e),
                error_type=type(e).__name__,
                exc_info=True
            )
            raise DatabaseError(f"Failed to count {self.model_class.__name__} entities", cause=e)

    def bulk_create(self, entities: List[T], session: Session) -> List[T]:
        """
        Create multiple entities in bulk.

        Args:
            entities: List of entities to create
            session: Database session

        Returns:
            List of created entities
        """
        try:
            session.add_all(entities)
            session.commit()

            for entity in entities:
                session.refresh(entity)

            self.logger.info(
                f"Bulk created {len(entities)} {self.model_class.__name__} entities"
            )
            return entities

        except Exception as e:
            session.rollback()
            self.logger.error(
                f"Error bulk creating {self.model_class.__name__} entities",
                entity_count=len(entities),
                error=str(e),
                error_type=type(e).__name__,
                exc_info=True
            )
            raise DatabaseError(f"Failed to bulk create {self.model_class.__name__} entities", cause=e)
