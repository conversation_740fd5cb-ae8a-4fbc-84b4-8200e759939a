"""Caching service for improved application performance."""

import redis
import json
import time
import os
from typing import Any, Dict
from app.core.config import settings
from app.core.exceptions import handle_cache_exceptions
from app.core.logging import LoggerMixin
from app.utils.cache_utils import get_cache_ttl


class CacheService(LoggerMixin):
    """Service for managing application caching with Redis."""

    def __init__(self):
        self.connection_pool = redis.ConnectionPool.from_url(
            settings.REDIS_URL,
            max_connections=50,
            retry_on_timeout=True,
            socket_keepalive=True,
            socket_keepalive_options={},
            health_check_interval=30,
        )

        self.redis_client = redis.Redis(
            connection_pool=self.connection_pool, decode_responses=False
        )

        self.get_default_ttl = get_cache_ttl
        self.token_bucket_lua_sha = None

        script_dir = os.path.dirname(os.path.abspath(__file__))
        lua_script_path = os.path.join(script_dir, "token_bucket.lua")

        try:
            with open(lua_script_path, "r") as f:
                token_bucket_lua_script = f.read()
                self.token_bucket_lua_sha = self.redis_client.script_load(
                    token_bucket_lua_script
                )
                self.logger.info("Token bucket Lua script loaded successfully")
        except FileNotFoundError:
            self.logger.error(
                f"Could not find the Lua script file at: {lua_script_path}"
            )
        except redis.RedisError as e:
            self.logger.error(f"Error loading Lua script to Redis: {e}")
        except Exception as e:
            self.logger.error(f"Failed to load token bucket Lua script: {e}")

    @handle_cache_exceptions
    async def set(self, key: str, value: Any, ttl: int = None) -> bool:
        if isinstance(value, (dict, list)):
            value = json.dumps(value)
        if not ttl:
            ttl = self.get_default_ttl(key)

        result = self.redis_client.set(key, value, ex=ttl)
        if result:
            self.logger.debug("Cache set successfully", key=key, ttl=ttl)

        return bool(result)

    @handle_cache_exceptions
    async def get(self, key: str) -> Any:
        serialized_value = self.redis_client.get(key)
        return json.loads(serialized_value)

    @handle_cache_exceptions
    async def delete(self, key: str) -> bool:
        result = self.redis_client.delete(key)
        if result:
            self.logger.debug("Cache key deleted", key=key)
        return bool(result)

    @handle_cache_exceptions
    async def exists(self, key: str) -> bool:
        return bool(self.redis_client.exists(key))

    @handle_cache_exceptions
    async def expire(self, key: str, ttl: int) -> bool:
        result = self.redis_client.expire(key, ttl)
        return bool(result)

    @handle_cache_exceptions
    async def ttl(self, key: str) -> int:
        """Get time to live for a key."""
        return self.redis_client.ttl(key)

    @handle_cache_exceptions
    async def increment(self, key: str, amount: int = 1) -> int:
        return self.redis_client.incrby(key, amount)

    @handle_cache_exceptions
    async def decrement(self, key: str, amount: int = 1) -> int:
        return self.redis_client.decrby(key, amount)

    async def get_cache_info(self) -> Dict[str, Any]:
        """Get enhanced cache information and statistics."""
        try:
            info = self.redis_client.info()
            memory_info = self.redis_client.info("memory")

            return {
                "is_connected": True,
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "0B"),
                "used_memory_peak": memory_info.get("used_memory_peak_human", "0B"),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "hit_rate": self._calculate_hit_rate(
                    info.get("keyspace_hits", 0), info.get("keyspace_misses", 0)
                ),
                "evicted_keys": info.get("evicted_keys", 0),
                "expired_keys": info.get("expired_keys", 0),
                "connection_pool_size": self.connection_pool.max_connections,
                "connection_pool_available": self.connection_pool.max_connections
                - len(self.connection_pool._available_connections),
            }
        except Exception as e:
            self.logger.error("Failed to get cache info", error=str(e))
            return {}

    def _calculate_hit_rate(self, hits: int, misses: int) -> float:
        """Calculate cache hit rate."""
        total = hits + misses
        return (hits / total * 100) if total > 0 else 0.0

    async def close(self):
        """Close all Redis connections."""
        try:
            self.redis_client.close()
            self.connection_pool.disconnect()
            self.logger.info("Cache service connections closed")
        except Exception as e:
            self.logger.error(f"Error closing cache connections: {e}")

    @handle_cache_exceptions
    async def token_bucket_rate_limit(
        self, key: str, capacity: int, rate: float, requested: int = 1
    ) -> bool:
        """
        Implements a token bucket rate limiting algorithm using a Redis Lua script for atomicity.

        Args:
            key: The unique identifier for the rate limit (e.g., user_id:api_endpoint).
            capacity: The maximum number of tokens the bucket can hold (the burst limit).
            rate: The rate at which tokens are refilled (tokens per second).
            requested: The number of tokens to consume for the current request.

        Returns:
            True if the request is allowed, False otherwise.
        """
        # Check if Lua script is loaded
        if self.token_bucket_lua_sha is None:
            self.logger.warning("Token bucket Lua script not loaded, allowing request")
            return True  # Fail open if script not available

        now = int(time.time())

        try:
            # Execute the Lua script on the Redis server
            result = self.redis_client.evalsha(
                self.token_bucket_lua_sha,
                1,  # Number of keys
                key,
                capacity,
                rate,
                now,
                requested,
            )

            if result == 1:
                self.logger.debug("Token bucket request allowed", key=key)
                return True
            else:
                self.logger.warning(
                    "Token bucket limit exceeded", key=key, capacity=capacity, rate=rate
                )
                return False
        except redis.exceptions.ResponseError as e:
            self.logger.error(f"Redis Lua script error: {e}", exc_info=True)
            return False


# Global cache service instance
cache_service = CacheService()
