"""
User authentication service for login, token management, and security operations.

Handles user authentication, token creation/validation, and security-related
user operations with proper error handling and logging.
"""

import time
from typing import Optional
from uuid import UUID
from sqlmodel import Session

from app.core.exceptions import AuthenticationError, ValidationError, BusinessLogicError
from app.core.security import (
    verify_password,
    create_access_token,
    create_refresh_token,
    get_password_hash,
)
from app.schemas.user_schema import User
from app.models.user_model import UserResponse
from app.models.auth_model import Token
from app.repositories.user_repository import UserRepository
from app.services.base_service import BaseService
from app.core.logging import log_business_event


class UserAuthenticationService(BaseService):
    """Service for user authentication and token management."""

    def __init__(self):
        super().__init__()
        self.user_repository = UserRepository()

    def authenticate_user(
        self, email: str, password: str, session: Session
    ) -> Optional[User]:
        """
        Authenticate user with email and password.

        Args:
            email: User email address
            password: User password
            session: Database session

        Returns:
            User if authentication successful, None otherwise
        """
        operation_start = time.time()

        try:
            # Get user by email
            user = self.user_repository.get_by_email(email, session)

            if not user:
                # Log failed authentication attempt
                safe_context = self._sanitize_sensitive_data({"email": email})
                self._log_user_operation(
                    "authentication_failed",
                    "unknown",
                    success=False,
                    reason="user_not_found",
                    **safe_context,
                )
                return None

            # Check if user is active
            if not user.is_active:
                self._log_user_operation(
                    "authentication_failed",
                    str(user.id),
                    success=False,
                    reason="user_inactive",
                    email=email,
                )
                return None

            # Verify password
            if not verify_password(password, user.hashed_password):
                self._log_user_operation(
                    "authentication_failed",
                    str(user.id),
                    success=False,
                    reason="invalid_password",
                    email=email,
                )
                return None

            # Log successful authentication
            self._log_user_operation(
                "authentication_successful", str(user.id), success=True, email=email
            )
            self._measure_performance(
                "user_authentication", operation_start, user_id=str(user.id)
            )

            return user

        except Exception as e:
            self.logger.error(
                "Unexpected error during authentication",
                error=str(e),
                error_type=type(e).__name__,
                email=email,
                exc_info=True,
            )
            return None

    def create_user_tokens(self, user: User) -> Token:
        """
        Create access and refresh tokens for authenticated user.

        Args:
            user: Authenticated user

        Returns:
            Token: Access and refresh tokens

        Raises:
            AuthenticationError: Token creation failed
        """
        try:
            # Create token data
            token_data = {
                "sub": str(user.id),
                "email": user.email,
                "role": user.role.value,
                "is_active": user.is_active,
            }

            # Create tokens
            access_token = create_access_token(data=token_data)
            refresh_token = create_refresh_token(data=token_data)

            # Log token creation
            log_business_event("tokens_created", user_id=str(user.id), email=user.email)

            return Token(
                access_token=access_token,
                token_type="bearer",
                refresh_token=refresh_token,
            )

        except Exception as e:
            self.logger.error(
                "Failed to create user tokens",
                error=str(e),
                error_type=type(e).__name__,
                user_id=str(user.id),
                exc_info=True,
            )
            raise AuthenticationError("Failed to create authentication tokens") from e

    def refresh_access_token(self, refresh_token: str, session: Session) -> Token:
        """
        Refresh access token using refresh token.

        Args:
            refresh_token: Valid refresh token
            session: Database session

        Returns:
            Token: New access and refresh tokens

        Raises:
            AuthenticationError: Invalid or expired refresh token
        """
        try:
            # Validate refresh token and extract user data
            from app.core.security import decode_token

            token_data = decode_token(refresh_token)
            user_id = UUID(token_data.get("sub"))

            # Get current user to ensure they're still active
            user = self.user_repository.get_by_id(user_id, session)

            if not user or not user.is_active:
                raise AuthenticationError("User not found or inactive")

            # Create new tokens
            new_tokens = self.create_user_tokens(user)

            # Log token refresh
            log_business_event(
                "tokens_refreshed", user_id=str(user.id), email=user.email
            )

            return new_tokens

        except AuthenticationError:
            raise
        except Exception as e:
            self.logger.error(
                "Failed to refresh access token",
                error=str(e),
                error_type=type(e).__name__,
                exc_info=True,
            )
            raise AuthenticationError("Failed to refresh access token") from e

    def change_password(
        self, user_id: UUID, current_password: str, new_password: str, session: Session
    ) -> UserResponse:
        """
        Change user password with current password verification.

        Args:
            user_id: User UUID
            current_password: Current password for verification
            new_password: New password
            session: Database session

        Returns:
            UserResponse: Updated user data

        Raises:
            AuthenticationError: Current password verification failed
            ValidationError: Invalid input
            BusinessLogicError: System error
        """
        try:
            if not user_id:
                raise ValidationError("User ID is required", field="user_id")

            if not current_password:
                raise ValidationError(
                    "Current password is required", field="current_password"
                )

            if not new_password:
                raise ValidationError("New password is required", field="new_password")

            # Get user
            user = self.user_repository.get_by_id(user_id, session)
            if not user:
                raise AuthenticationError("User not found")

            # Verify current password
            if not verify_password(current_password, user.hashed_password):
                self.logger.warning(
                    "password_change_failed",
                    str(user_id),
                    success=False,
                    reason="invalid_current_password",
                )
                raise AuthenticationError("Current password is incorrect")

            # Validate new password strength
            self._validate_password_strength(new_password)

            # Hash new password
            user.hashed_password = get_password_hash(new_password)

            # Update user
            updated_user = self.user_repository.update(user, session)

            # Log successful password change
            self.logger.info(
                "password_changed", str(user_id), success=True, email=user.email
            )

            return UserResponse.model_validate(updated_user)

        except (AuthenticationError, ValidationError):
            raise
        except Exception as e:
            self.logger.error(
                "Unexpected error changing password",
                error=str(e),
                error_type=type(e).__name__,
                user_id=str(user_id),
                exc_info=True,
            )
            raise BusinessLogicError(
                "Failed to change password due to system error",
                error_code="SYSTEM_ERROR",
            ) from e

    def reset_password(
        self, user_id: UUID, new_password: str, session: Session
    ) -> UserResponse:
        """
        Reset user password (admin operation).

        Args:
            user_id: User UUID
            new_password: New password
            session: Database session

        Returns:
            UserResponse: Updated user data

        Raises:
            ValidationError: Invalid input
            BusinessLogicError: System error
        """
        try:
            if not user_id:
                raise ValidationError("User ID is required", field="user_id")

            if not new_password:
                raise ValidationError("New password is required", field="new_password")

            # Get user
            user = self.user_repository.get_by_id(user_id, session)
            if not user:
                raise ValidationError("User not found", field="user_id")

            # Validate new password strength
            self._validate_password_strength(new_password)

            # Hash new password
            user.hashed_password = get_password_hash(new_password)

            # Update user
            updated_user = self.user_repository.update(user, session)

            # Log password reset
            self.logger.info(
                "password_reset", str(user_id), success=True, email=user.email
            )

            return UserResponse.model_validate(updated_user)

        except ValidationError:
            raise
        except Exception as e:
            self.logger.error(
                "Unexpected error resetting password",
                error=str(e),
                error_type=type(e).__name__,
                user_id=str(user_id),
                exc_info=True,
            )
            raise BusinessLogicError(
                "Failed to reset password due to system error",
                error_code="SYSTEM_ERROR",
            ) from e

    def _validate_password_strength(self, password: str):
        """
        Validate password strength using existing security utilities.

        Args:
            password: Password to validate

        Raises:
            ValidationError: Password doesn't meet requirements
        """
        from app.core.security import _validate_password_strength

        try:
            _validate_password_strength(password)
        except ValueError as e:
            raise ValidationError(str(e), field="password") from e
