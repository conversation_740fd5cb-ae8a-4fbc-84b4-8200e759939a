"""
User service for handling user-related business logic.

This service provides a unified interface for user management operations
using the repository pattern and authentication service.
"""

from typing import Optional
from uuid import UUID
from sqlmodel import Session

from app.core.logging import get_logger
from app.core.exceptions import ValidationError, NotFoundError, BusinessLogicError
from app.models.user_model import UserCreate, UserResponse
from app.models.auth_model import Token
from app.models.common_models import PaginationParams, PaginatedResponse
from app.schemas.user_schema import UserRoleEnum, User, Account
from app.repositories.user_repository import UserRepository
from app.services.auth_service import UserAuthenticationService
from app.services.base_service import BaseService

logger = get_logger(__name__)


class UserService(BaseService):
    """
    User service for handling user-related business logic.

    Provides CRUD operations and coordinates with authentication service.
    """

    def __init__(self):
        super().__init__()
        self.user_repository = UserRepository()
        self.auth_service = UserAuthenticationService()

    def create_user(self, user_data: UserCreate, session: Session) -> UserResponse:
        """
        Create a new user with account.

        Args:
            user_data: User creation data
            session: Database session

        Returns:
            UserResponse: Created user data

        Raises:
            ValidationError: Invalid input data
            BusinessLogicError: Business rule violations
        """
        try:
            # Check if user already exists
            existing_user = self.user_repository.get_by_email(user_data.email, session)
            if existing_user:
                raise BusinessLogicError(f"User with email {user_data.email} already exists")

            # Hash password
            hashed_password = self.auth_service.hash_password(user_data.password)

            # Create user
            user = User(
                email=user_data.email,
                phone_number=user_data.phone_number,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                hashed_password=hashed_password,
                role=user_data.role,
                is_active=True,
                is_2fa_enabled=False,
            )

            # Create user first
            created_user = self.user_repository.create(user, session)

            # Create account for the user
            account = Account(
                user_id=created_user.id,
                account_name=f"{user_data.first_name} {user_data.last_name}",
                account_type="individual",
                is_active=True,
            )

            # Add account to session
            session.add(account)
            session.commit()
            session.refresh(account)
            session.refresh(created_user)

            logger.info("User created successfully", user_id=str(created_user.id), email=user_data.email)
            return UserResponse.model_validate(created_user, from_attributes=True)

        except Exception as e:
            logger.error("Failed to create user", error=str(e), email=user_data.email)
            raise

    def authenticate_user(self, email: str, password: str, session: Session) -> Optional[User]:
        """Authenticate user credentials."""
        return self.auth_service.authenticate_user(email, password, session)

    def create_user_tokens(self, user: User) -> Token:
        """Create access and refresh tokens for user."""
        return self.auth_service.create_user_tokens(user)

    def get_user_by_id(self, user_id: UUID, session: Session) -> UserResponse:
        """Get user by ID."""
        user = self.user_repository.get_by_id(user_id, session)
        if not user:
            raise NotFoundError(f"User with ID {user_id} not found")
        return UserResponse.model_validate(user, from_attributes=True)

    def get_user_by_email(self, email: str, session: Session) -> UserResponse:
        """Get user by email."""
        user = self.user_repository.get_by_email(email, session)
        if not user:
            raise NotFoundError(f"User with email {email} not found")
        return UserResponse.model_validate(user, from_attributes=True)

    def list_users(
        self,
        session: Session,
        pagination: PaginationParams,
        role_filter: Optional[UserRoleEnum] = None,
        active_only: bool = True
    ) -> PaginatedResponse[UserResponse]:
        """Get paginated list of users."""
        # Build filters
        filters = {}
        if active_only:
            filters["is_active"] = True
        if role_filter:
            filters["role"] = role_filter.value

        # Get paginated users
        paginated_users = self.user_repository.get_paginated(
            session=session,
            pagination=pagination,
            filters=filters
        )

        # Convert to response models
        user_responses = [
            UserResponse.model_validate(user, from_attributes=True)
            for user in paginated_users.items
        ]

        return PaginatedResponse(
            items=user_responses,
            meta=paginated_users.meta
        )

    def deactivate_user(self, user_id: UUID, session: Session) -> UserResponse:
        """Deactivate user account."""
        user = self.user_repository.get_by_id(user_id, session)
        if not user:
            raise NotFoundError(f"User with ID {user_id} not found")

        user.is_active = False
        updated_user = self.user_repository.update(user, session)

        logger.info("User deactivated", user_id=str(user_id))
        return UserResponse.model_validate(updated_user, from_attributes=True)


# Global service instance
user_service = UserService()