-- KEYS[1] = key
-- ARGV[1] = capacity
-- ARGV[2] = rate (tokens per second)
-- ARGV[3] = now (current Unix timestamp)
-- ARGV[4] = requested (number of tokens to consume)

local key = KEYS[1]
local capacity = tonumber(ARGV[1])
local rate = tonumber(ARGV[2])
local now = tonumber(ARGV[3])
local requested = tonumber(ARGV[4])

local last_refill_time = tonumber(redis.call('HGET', key, 'last_refill_time'))
if not last_refill_time then
    last_refill_time = 0
end

local tokens = tonumber(redis.call('HGET', key, 'tokens'))
if not tokens then
    tokens = capacity
end

local time_passed = now - last_refill_time
if time_passed > 0 then
    local tokens_to_add = time_passed * rate
    tokens = math.min(capacity, tokens + tokens_to_add)
    last_refill_time = now
end

if tokens >= requested then
    tokens = tokens - requested
    redis.call('HSET', key, 'tokens', tokens, 'last_refill_time', last_refill_time)
    redis.call('EXPIRE', key, capacity / rate * 2) -- Set TTL to at least twice the time to refill
    return 1
else
    return 0
end