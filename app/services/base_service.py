"""
Base service class with common patterns for error handling, validation, and logging.

Provides reusable methods for consistent service layer implementation.
"""

import time
from typing import Any, Dict
from uuid import UUID
from sqlmodel import Session

from app.core.logging import get_logger, log_business_event, log_performance_metric
from app.core.exceptions import ValidationError, NotFoundError, BusinessLogicError
from app.utils.string_utils import mask_sensitive_data


class BaseService:
    """Base service class with common patterns."""

    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)

    def _validate_required_field(self, value: Any, field_name: str, field_type: str = "field"):
        """Validate that a required field is provided."""
        if not value:
            raise ValidationError(f"{field_name.title()} is required", field=field_name)

    def _validate_uuid_field(self, value: Any, field_name: str):
        """Validate UUID field."""
        if not value:
            raise ValidationError(f"{field_name.title()} is required", field=field_name)
        
        if not isinstance(value, UUID):
            try:
                UUID(str(value))
            except (ValueError, TypeError):
                raise ValidationError(f"Invalid {field_name} format", field=field_name)

    def _handle_not_found(self, entity_name: str, identifier: str, field_name: str = "id"):
        """Raise standardized not found error."""
        raise NotFoundError(
            f"{entity_name.title()} with {field_name} {identifier} not found",
            resource=entity_name.lower(),
            identifier=identifier
        )

    def _handle_unexpected_error(self, operation: str, error: Exception, context: Dict[str, Any] = None):
        """Handle unexpected errors with consistent logging and exception raising."""
        context = context or {}
        
        self.logger.error(
            f"Unexpected error during {operation}",
            error=str(error),
            error_type=type(error).__name__,
            exc_info=True,
            **context
        )
        
        raise BusinessLogicError(
            f"Failed to {operation} due to system error",
            error_code="SYSTEM_ERROR"
        ) from error

    def _log_operation_success(self, operation: str, entity_id: str, **kwargs):
        """Log successful business operation."""
        log_business_event(
            operation,
            entity_id=entity_id,
            **kwargs
        )

    def _log_user_operation(self, operation: str, user_id: str, success: bool = True, **kwargs):
        """Log user-specific operation."""
        # log_user_operation(
        #     operation,
        #     user_id,
        #     success=success,
        #     **kwargs
        # )

    def _measure_performance(self, operation: str, start_time: float, **kwargs):
        """Measure and log operation performance."""
        duration = time.time() - start_time
        log_performance_metric(
            operation,
            duration=duration,
            **kwargs
        )

    def _sanitize_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize sensitive data for logging."""
        return mask_sensitive_data(data)

    def _execute_with_error_handling(self, operation_name: str, operation_func, *args, **kwargs):
        """Execute operation with standardized error handling."""
        operation_start = time.time()
        
        try:
            result = operation_func(*args, **kwargs)
            self._measure_performance(operation_name, operation_start)
            return result
            
        except (ValidationError, NotFoundError, BusinessLogicError):
            # Re-raise known business exceptions
            raise
        except Exception as e:
            # Handle unexpected errors
            self._handle_unexpected_error(operation_name, e, kwargs)

    def _get_entity_by_id(self, repository, entity_id: UUID, session: Session, entity_name: str):
        """Generic method to get entity by ID with error handling."""
        self._validate_uuid_field(entity_id, f"{entity_name}_id")
        
        entity = repository.get_by_id(entity_id, session)
        if not entity:
            self._handle_not_found(entity_name, str(entity_id))
        
        return entity

    def _get_entity_by_field(self, repository, field_value: Any, field_name: str, session: Session, entity_name: str):
        """Generic method to get entity by field with error handling."""
        self._validate_required_field(field_value, field_name)
        
        get_method = getattr(repository, f"get_by_{field_name}")
        entity = get_method(field_value, session)
        
        if not entity:
            self._handle_not_found(entity_name, str(field_value), field_name)
        
        return entity
