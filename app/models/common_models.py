"""
Common models for pagination, search, and API responses.

Provides reusable Pydantic models for consistent data structures
across the application.
"""

from typing import List, Dict, Any, Optional, TypeVar, Generic
from pydantic import BaseModel, Field

T = TypeVar("T")


class PaginationParams(BaseModel):
    """Pagination parameters for database queries."""

    page: int = Field(default=1, ge=1, description="Page number (1-based)")
    size: int = Field(default=20, ge=1, le=100, description="Items per page")

    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.size


class PaginationMeta(BaseModel):
    """Pagination metadata for responses."""

    page: int = Field(description="Current page number")
    size: int = Field(description="Items per page")
    total: int = Field(description="Total number of items")
    pages: int = Field(description="Total number of pages")
    has_next: bool = Field(description="Whether there is a next page")
    has_prev: bool = Field(description="Whether there is a previous page")


class PaginatedResponse(BaseModel, Generic[T]):
    """Generic paginated response with metadata."""

    items: List[T] = Field(description="List of items for current page")
    meta: PaginationMeta = Field(description="Pagination metadata")


class SearchParams(BaseModel):
    """Search and filtering parameters."""

    query: str = Field(description="Search query string")
    fields: List[str] = Field(default=[], description="Fields to search in")
    filters: Dict[str, Any] = Field(default={}, description="Additional filters")
    sort_by: Optional[str] = Field(default=None, description="Field to sort by")
    sort_order: str = Field(
        default="asc", pattern="^(asc|desc)$", description="Sort order (asc/desc)"
    )

