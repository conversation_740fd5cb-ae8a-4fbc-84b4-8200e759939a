"""
User Pydantic models for API request/response validation.

Contains DTOs for user operations and data transfer.
"""

from typing import Optional
from pydantic import BaseModel, EmailStr, field_validator
from datetime import datetime
from app.schemas.user_schema import UserRoleEnum
from uuid import UUID
import re


class UserCreate(BaseModel):
    email: EmailStr
    password: str
    phone_number: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    image: Optional[str] = None
    role: UserRoleEnum

    @field_validator("password")
    @classmethod
    def validate_password_complexity(cls, value: str) -> str:
        if not re.search(r"[a-z]", value):
            raise ValueError("Password must contain at least one lowercase letter")
        if not re.search(r"[A-Z]", value):
            raise ValueError("Password must contain at least one uppercase letter")
        if not re.search(r"\d", value):
            raise ValueError("Password must contain at least one digit")
        if not re.search(r"[@$!%*?&]", value):
            raise ValueError(
                "Password must contain at least one special character (@$!%*?&)"
            )
        return value


class AccountResponse(BaseModel):
    id: UUID
    user_id: UUID
    access_token: Optional[str] = None
    expires_at: Optional[datetime] = None
    refresh_token: Optional[str] = None
    refresh_token_expires_at: Optional[datetime] = None
    last_login: Optional[datetime] = None
    last_password_change: Optional[datetime] = None
    last_failed_login_attempts: int
    last_failed_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime


class UserResponse(BaseModel):
    id: UUID
    email: EmailStr
    phone_number: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    image: Optional[str] = None
    role: UserRoleEnum
    account: Optional[AccountResponse] = None
    is_active: bool
    is_2fa_enabled: bool
    created_at: datetime
    updated_at: datetime
