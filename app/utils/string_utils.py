"""
String manipulation and validation utilities.

Provides string operations, validation, and formatting functions.
"""

import re
from typing import List, Optional


def is_valid_url(url: str) -> bool:
    """Validate URL format."""
    if not url:
        return False

    url_pattern = re.compile(
        r"^https?://"  # http:// or https://
        r"(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|"  # domain...
        r"localhost|"  # localhost...
        r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})"  # ...or ip
        r"(?::\d+)?"  # optional port
        r"(?:/?|[/?]\S+)$",
        re.IGNORECASE,
    )

    return bool(url_pattern.match(url))


def extract_domain(url: str) -> Optional[str]:
    """Extract domain from URL."""
    if not url:
        return None

    # Add protocol if missing
    if not url.startswith(("http://", "https://")):
        url = f"https://{url}"

    try:
        from urllib.parse import urlparse

        parsed = urlparse(url)
        return parsed.netloc.lower()
    except Exception:
        return None


def remove_extra_whitespace(text: str) -> str:
    """Replace multiple whitespace with single space"""
    cleaned = re.sub(r"\s+", " ", text.strip())
    return cleaned


def slugify(text: str, max_length: int = 50) -> str:
    """Convert text to URL-friendly slug."""
    if not text:
        return ""
    
    slug = text.lower()

    # Replace spaces and special characters with hyphens
    slug = re.sub(r"[^\w\s-]", "", slug)
    slug = re.sub(r"[-\s]+", "-", slug)

    # Remove leading/trailing hyphens
    slug = slug.strip("-")

    # Truncate if necessary
    if len(slug) > max_length:
        slug = slug[:max_length].rstrip("-")

    return slug


def generate_initials(name: str, max_chars: int = 2) -> str:
    """Generate initials from name."""
    if not name:
        return ""

    words = name.strip().split()
    initials = "".join(word[0].upper() for word in words if word)

    return initials[:max_chars]


def mask_sensitive_data(text: str, mask_char: str = "*", visible_chars: int = 2) -> str:
    """Mask sensitive data showing only first/last characters."""
    if not text or len(text) <= visible_chars * 2:
        return mask_char * len(text) if text else ""

    start = text[:visible_chars]
    end = text[-visible_chars:]
    middle_length = len(text) - (visible_chars * 2)

    return f"{start}{mask_char * middle_length}{end}"


def count_words(text: str) -> int:
    """Count words in text."""
    if not text:
        return 0

    # Split by whitespace and filter empty strings
    words = [word for word in text.split() if word.strip()]
    return len(words)


def estimate_reading_time(text: str, words_per_minute: int = 200) -> int:
    """Estimate reading time for text."""
    word_count = count_words(text)
    reading_time = max(1, round(word_count / words_per_minute))
    return reading_time


def highlight_search_terms(
    text: str,
    search_terms: List[str],
    highlight_start: str = "<mark>",
    highlight_end: str = "</mark>",
) -> str:
    """Highlight search terms in text."""
    if not text or not search_terms:
        return text

    highlighted = text

    for term in search_terms:
        if term:
            # Case-insensitive replacement
            pattern = re.compile(re.escape(term), re.IGNORECASE)
            highlighted = pattern.sub(
                f"{highlight_start}{term}{highlight_end}", highlighted
            )

    return highlighted


def snake_to_camel_case(snake_str: str) -> str:
    """Convert snake_case to camelCase."""
    components = snake_str.split("_")
    return components[0] + "".join(word.capitalize() for word in components[1:])


def camel_to_snake_case(camel_str: str) -> str:
    """Convert camelCase to snake_case."""
    s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", camel_str)
    return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()
