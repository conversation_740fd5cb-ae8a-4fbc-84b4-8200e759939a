"""
Date and time utility functions.

Provides consistent date/time operations, formatting, and calculations.
"""

from datetime import datetime, timezone, timedelta


def calculate_age(birth_date: datetime) -> int:
    """Calculate age from birth date."""
    today = datetime.now(timezone.utc)
    return (
        today.year
        - birth_date.year
        - ((today.month, today.day) < (birth_date.month, birth_date.day))
    )


def is_expired(expiry_time: datetime) -> bool:
    """Check if a datetime has expired."""
    return datetime.now(timezone.utc) > expiry_time


def time_until_expiry(expiry_time: datetime) -> timedelta:
    """Calculate time remaining until expiry."""
    return expiry_time - datetime.now(timezone.utc)


def format_datetime(dt: datetime, format_string: str = "%Y-%m-%d %H:%M:%S UTC") -> str:
    """Format datetime to string."""
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)
    return dt.strftime(format_string)


def parse_datetime(dt_string: str) -> datetime:
    """Parse datetime string to datetime object."""
    try:
        # Try ISO format first
        return datetime.fromisoformat(dt_string.replace("Z", "+00:00"))
    except ValueError:
        # Try common formats
        formats = ["%Y-%m-%d %H:%M:%S", "%Y-%m-%d", "%d/%m/%Y %H:%M:%S", "%d/%m/%Y"]

        for fmt in formats:
            try:
                return datetime.strptime(dt_string, fmt).replace(tzinfo=timezone.utc)
            except ValueError:
                continue

        raise ValueError(f"Unable to parse datetime string: {dt_string}")
