"""
Input validation and sanitization utilities.

Provides validation functions for user input, data integrity,
and security-focused input sanitization.
"""

import re
import html
from typing import List, Any, Tuple, Optional



def sanitize_input(value: str, max_length: Optional[int] = None) -> str:
    """Sanitize string input by removing dangerous characters."""
    if not isinstance(value, str):
        return str(value)

    # Remove null bytes and control characters
    sanitized = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "", value)

    # Remove potential SQL injection patterns
    dangerous_patterns = [
        r"(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b)",
        r"(--|\/\*|\*\/)",
        r"(\bOR\b\s+\d+\s*=\s*\d+|\bAND\b\s+\d+\s*=\s*\d+)",
    ]

    for pattern in dangerous_patterns:
        sanitized = re.sub(pattern, "", sanitized, flags=re.IGNORECASE)

    sanitized = sanitized.strip()

    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length]

    return sanitized


def validate_json_schema(
    data: Any, required_fields: List[str]
) -> Tuple[bool, List[str]]:
    """Validate JSON structure has required fields."""
    if not isinstance(data, dict):
        return False, ["Data must be a JSON object"]

    missing_fields = [field for field in required_fields if field not in data]
    return len(missing_fields) == 0, missing_fields


def sanitize_search_query(query: str) -> str:
    """Sanitize search query to prevent injection attacks."""
    if not query:
        return ""

    # Remove special regex characters that could cause issues
    sanitized = re.sub(r"[.*+?^${}()|[\]\\]", "", query)
    return sanitized[:100].strip()


def sanitize_html(text: str) -> str:
    """Basic HTML sanitization."""
    if not text:
        return ""

    # Remove HTML tags and decode entities
    sanitized = re.sub(r"<[^>]+>", "", text)
    sanitized = html.unescape(sanitized)
    return sanitized.strip()
