"""
Batch processing and retry utilities.

Provides functions for processing data in batches with retry logic and error handling.
"""

import asyncio
import time
from typing import List, Any, Callable, Dict, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed


def process_in_batches(
    items: List[Any],
    batch_size: int,
    processor: Callable[[List[Any]], Any],
    max_workers: int = 4,
) -> List[Any]:
    """Process items in batches using thread pool."""
    if not items:
        return []

    # Split items into batches
    batches = [items[i : i + batch_size] for i in range(0, len(items), batch_size)]
    results = []

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all batches
        future_to_batch = {
            executor.submit(processor, batch): batch for batch in batches
        }

        # Collect results as they complete
        for future in as_completed(future_to_batch):
            try:
                result = future.result()
                results.append(result)
            except Exception:
                results.append(None)

    return results


async def async_process_in_batches(
    items: List[Any],
    batch_size: int,
    processor: Callable[[List[Any]], Any],
    max_concurrent: int = 4,
) -> List[Any]:
    """Process items in batches asynchronously."""
    if not items:
        return []

    # Split items into batches
    batches = [items[i : i + batch_size] for i in range(0, len(items), batch_size)]

    # Create semaphore to limit concurrency
    semaphore = asyncio.Semaphore(max_concurrent)

    async def process_batch_with_semaphore(batch):
        async with semaphore:
            try:
                result = await processor(batch)
                return result
            except Exception:
                return None

    # Process all batches concurrently
    tasks = [process_batch_with_semaphore(batch) for batch in batches]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    return results


def retry_with_backoff(
    func: Callable,
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
    exceptions: Tuple = (Exception,),
) -> Any:
    """
    Retry function with exponential backoff for both sync and async operations.

    Args:
        func: Function to retry (can be sync or async)
        max_retries: Maximum number of retries
        base_delay: Initial delay between retries
        max_delay: Maximum delay between retries
        backoff_factor: Backoff multiplier
        exceptions: Tuple of exceptions to catch and retry on

    Returns:
        Function result or raises last exception
    """
    last_exception = None

    for attempt in range(max_retries + 1):
        try:
            if asyncio.iscoroutinefunction(func):
                return asyncio.run(func())
            else:
                return func()

        except exceptions as e:
            last_exception = e
            if attempt == max_retries:
                break
            delay = min(base_delay * (backoff_factor**attempt), max_delay)
            time.sleep(delay)

    raise last_exception


class BatchProcessor:
    """Class for processing items in batches with error handling and metrics."""

    def __init__(
        self,
        batch_size: int = 100,
        max_workers: int = 4,
        retry_attempts: int = 3,
        retry_delay: float = 1.0,
    ):
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.retry_attempts = retry_attempts
        self.retry_delay = retry_delay
        self.stats = {
            "total_items": 0,
            "processed_items": 0,
            "failed_items": 0,
            "batches_processed": 0,
            "batches_failed": 0,
            "start_time": None,
            "end_time": None,
        }

    def process(
        self, items: List[Any], processor: Callable[[List[Any]], List[Any]]
    ) -> Dict[str, Any]:
        """Process items in batches and return results with statistics."""
        self.stats["total_items"] = len(items)
        self.stats["start_time"] = time.time()

        if not items:
            self.stats["end_time"] = time.time()
            return {"results": [], "stats": self.stats}

        # Split into batches
        batches = [
            items[i : i + self.batch_size]
            for i in range(0, len(items), self.batch_size)
        ]

        all_results = []

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_batch = {}

            for batch in batches:
                # Wrap processor with retry logic
                def process_with_retry():
                    return retry_with_backoff(
                        lambda: processor(batch),
                        max_retries=self.retry_attempts,
                        base_delay=self.retry_delay,
                    )

                future = executor.submit(process_with_retry)
                future_to_batch[future] = batch

            # Collect results
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                try:
                    batch_results = future.result()
                    all_results.extend(batch_results)
                    self.stats["processed_items"] += len(batch_results)
                    self.stats["batches_processed"] += 1

                except Exception:
                    self.stats["failed_items"] += len(batch)
                    self.stats["batches_failed"] += 1

        self.stats["end_time"] = time.time()
        self.stats["processing_time"] = (
            self.stats["end_time"] - self.stats["start_time"]
        )

        return {"results": all_results, "stats": self.stats.copy()}

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary of last processing run."""
        if not self.stats["start_time"]:
            return {"error": "No processing run completed yet"}

        processing_time = self.stats.get("processing_time", 0)
        success_rate = (
            self.stats["processed_items"] / self.stats["total_items"] * 100
            if self.stats["total_items"] > 0
            else 0
        )

        items_per_second = (
            self.stats["processed_items"] / processing_time
            if processing_time > 0
            else 0
        )

        return {
            "total_items": self.stats["total_items"],
            "processed_items": self.stats["processed_items"],
            "failed_items": self.stats["failed_items"],
            "success_rate_percent": round(success_rate, 2),
            "processing_time_seconds": round(processing_time, 2),
            "items_per_second": round(items_per_second, 2),
            "batches_processed": self.stats["batches_processed"],
            "batches_failed": self.stats["batches_failed"],
        }
