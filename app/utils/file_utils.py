"""
File handling and validation utilities.

Provides file operations, validation, and naming utilities.
"""

from pathlib import Path
from typing import Optional
from uuid import UUID
from datetime import datetime, timezone

from app.core.security import generate_random_string
from app.core.security_constants import (
    FILE_UPLOAD_CONFIG,
    is_allowed_file_extension,
)


def generate_filename(original_filename: str, user_id: UUID = None) -> str:
    """Generate a unique filename for file uploads."""
    timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
    random_suffix = generate_random_string(8)

    # Extract file extension
    if "." in original_filename:
        _, ext = original_filename.rsplit(".", 1)
        ext = f".{ext.lower()}"
    else:
        ext = ""

    if user_id:
        return f"{user_id}_{timestamp}_{random_suffix}{ext}"
    else:
        return f"{timestamp}_{random_suffix}{ext}"


def get_file_extension(filename: str) -> str:
    """Get file extension from filename."""
    if not filename:
        return ""

    path = Path(filename)
    return path.suffix.lower()


def get_file_type_category(filename: str) -> str:
    """Get file type category based on extension using security constants."""
    ext = get_file_extension(filename).lower()

    # Use security constants for categorization
    for category, extensions in FILE_UPLOAD_CONFIG["allowed_extensions"].items():
        if ext in extensions:
            return category

    return "other"


def is_safe_file_size(file_size: int, max_size_mb: int = None) -> bool:
    """Check if file size is within allowed limits using security constants."""
    if max_size_mb is None:
        max_size_bytes = FILE_UPLOAD_CONFIG["max_file_size"]
    else:
        max_size_bytes = max_size_mb * 1024 * 1024
    return 0 < file_size <= max_size_bytes


def generate_file_path(
    base_dir: str,
    filename: str,
    user_id: Optional[UUID] = None,
    date_based: bool = True,
) -> str:
    """Generate organized file path for storage."""
    path_parts = [base_dir]

    if date_based:
        now = datetime.now(timezone.utc)
        path_parts.extend([str(now.year), f"{now.month:02d}"])

    if user_id:
        path_parts.append(str(user_id))

    unique_filename = generate_filename(filename, user_id)
    path_parts.append(unique_filename)

    return "/".join(path_parts)


def validate_file_by_type(
    filename: str, file_size: int, file_type: str, max_size_mb: int = None
) -> dict:
    """
    Args:
        filename: Name of the file
        file_size: Size of the file in bytes
        file_type: Type category (image, document, spreadsheet, archive)
        max_size_mb: Maximum size in MB (uses security constants if None)

    Returns:
        Validation result dictionary
        {
            "is_valid": bool,
            "errors": List[str],
            "warnings": List[str]
        }
    """
    result = {"is_valid": True, "errors": [], "warnings": []}

    # Validate file extension using security constants
    if not is_allowed_file_extension(filename, file_type=file_type):
        allowed_extensions = FILE_UPLOAD_CONFIG["allowed_extensions"].get(file_type, [])
        result["is_valid"] = False
        result["errors"].append(
            f"Invalid file type. Allowed {file_type} extensions: {', '.join(allowed_extensions)}"
        )

    # Validate file size
    if not is_safe_file_size(file_size, max_size_mb):
        max_size = max_size_mb or (FILE_UPLOAD_CONFIG["max_file_size"] // (1024 * 1024))
        result["is_valid"] = False
        result["errors"].append(f"File size exceeds {max_size}MB limit")

    # Check filename length
    if len(filename) > 255:
        result["warnings"].append("Filename is very long and will be truncated")

    return result


def validate_image_file(filename: str, file_size: int, max_size_mb: int = 5) -> dict:
    return validate_file_by_type(filename, file_size, "image", max_size_mb)


def validate_document_file(
    filename: str, file_size: int, max_size_mb: int = 10
) -> dict:
    return validate_file_by_type(filename, file_size, "document", max_size_mb)


def get_mime_type(filename: str) -> str:
    """Get MIME type based on file extension."""
    ext = get_file_extension(filename).lower()

    # Common MIME types mapping
    mime_types = {
        # Images
        ".jpg": "image/jpeg",
        ".jpeg": "image/jpeg",
        ".png": "image/png",
        ".gif": "image/gif",
        ".webp": "image/webp",
        ".svg": "image/svg+xml",
        # Documents
        ".pdf": "application/pdf",
        ".doc": "application/msword",
        ".txt": "text/plain",
        ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        # Spreadsheets
        ".xls": "application/vnd.ms-excel",
        ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ".csv": "text/csv",
        # Archives
        ".zip": "application/zip",
        ".tar": "application/x-tar",
        ".gz": "application/gzip",
        # Audio/Video (basic support)
        ".mp3": "audio/mpeg",
        ".mp4": "video/mp4",
    }

    return mime_types.get(ext, "application/octet-stream")
