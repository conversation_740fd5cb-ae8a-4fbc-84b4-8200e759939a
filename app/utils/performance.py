"""
Performance monitoring utilities for system metrics collection.

Provides functions to collect CPU usage, memory usage, response times,
and other performance metrics for monitoring and health checks.
"""

import time
import psutil
from typing import Dict, Any
from datetime import datetime, timezone
from app.core.logging import get_logger

logger = get_logger(__name__)

# Global variables for tracking
_start_time = time.time()
_request_count = 0
_total_response_time = 0.0


def get_system_metrics() -> Dict[str, Any]:
    """Get current system performance metrics."""
    try:
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=0.1)
        cpu_count = psutil.cpu_count()

        # Memory metrics
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / (1024 * 1024)
        memory_total_mb = memory.total / (1024 * 1024)

        # Disk metrics
        disk = psutil.disk_usage("/")
        disk_percent = disk.percent
        disk_used_gb = disk.used / (1024 * 1024 * 1024)
        disk_total_gb = disk.total / (1024 * 1024 * 1024)

        return {
            "cpu": {
                "usage_percent": round(cpu_percent, 2),
                "core_count": cpu_count,
                "status": "healthy"
                if cpu_percent < 80
                else "warning"
                if cpu_percent < 95
                else "critical",
            },
            "memory": {
                "usage_percent": round(memory_percent, 2),
                "used_mb": round(memory_used_mb, 2),
                "total_mb": round(memory_total_mb, 2),
                "available_mb": round((memory.available) / (1024 * 1024), 2),
                "status": "healthy"
                if memory_percent < 80
                else "warning"
                if memory_percent < 95
                else "critical",
            },
            "disk": {
                "usage_percent": round(disk_percent, 2),
                "used_gb": round(disk_used_gb, 2),
                "total_gb": round(disk_total_gb, 2),
                "free_gb": round((disk.free) / (1024 * 1024 * 1024), 2),
                "status": "healthy"
                if disk_percent < 80
                else "warning"
                if disk_percent < 95
                else "critical",
            },
        }

    except Exception as e:
        logger.error("Failed to get system metrics", error=str(e), exc_info=True)
        return {
            "cpu": {"error": str(e), "status": "unknown"},
            "memory": {"error": str(e), "status": "unknown"},
            "disk": {"error": str(e), "status": "unknown"},
        }


def get_process_metrics() -> Dict[str, Any]:
    """
    Get current process performance metrics.

    Returns:
        Dictionary containing process metrics
    """
    try:
        process = psutil.Process()

        # Process CPU and memory
        cpu_percent = process.cpu_percent()
        memory_info = process.memory_info()
        memory_percent = process.memory_percent()

        # Process details
        create_time = process.create_time()
        uptime_seconds = time.time() - create_time

        # Thread and file descriptor counts
        num_threads = process.num_threads()
        try:
            num_fds = process.num_fds()  # Unix only
        except (AttributeError, psutil.AccessDenied):
            num_fds = None

        return {
            "cpu_percent": round(cpu_percent, 2),
            "memory": {
                "rss_mb": round(memory_info.rss / (1024 * 1024), 2),
                "vms_mb": round(memory_info.vms / (1024 * 1024), 2),
                "percent": round(memory_percent, 2),
            },
            "uptime_seconds": round(uptime_seconds, 2),
            "uptime_formatted": format_uptime(uptime_seconds),
            "threads": num_threads,
            "file_descriptors": num_fds,
            "status": "healthy"
            if cpu_percent < 50 and memory_percent < 50
            else "warning",
        }

    except Exception as e:
        logger.error("Failed to get process metrics", error=str(e), exc_info=True)
        return {"error": str(e), "status": "unknown"}


def get_application_metrics() -> Dict[str, Any]:
    """
    Get application-specific performance metrics.

    Returns:
        Dictionary containing application metrics
    """
    global _start_time, _request_count, _total_response_time

    try:
        current_time = time.time()
        app_uptime = current_time - _start_time

        # Calculate average response time
        avg_response_time = (
            (_total_response_time / _request_count) if _request_count > 0 else 0
        )

        return {
            "uptime_seconds": round(app_uptime, 2),
            "uptime_formatted": format_uptime(app_uptime),
            "total_requests": _request_count,
            "average_response_time_ms": round(avg_response_time * 1000, 2),
            "requests_per_second": round(_request_count / app_uptime, 2)
            if app_uptime > 0
            else 0,
            "start_time": datetime.fromtimestamp(
                _start_time, tz=timezone.utc
            ).isoformat(),
            "status": "healthy",
        }

    except Exception as e:
        logger.error("Failed to get application metrics", error=str(e), exc_info=True)
        return {"error": str(e), "status": "unknown"}


def record_request_metrics(response_time: float) -> None:
    """Record metrics for a completed request."""
    global _request_count, _total_response_time

    try:
        _request_count += 1
        _total_response_time += response_time
    except Exception as e:
        logger.error("Failed to record request metrics", error=str(e))


def format_uptime(seconds: float) -> str:
    """Format uptime seconds into human-readable string."""
    try:
        days = int(seconds // 86400)
        hours = int((seconds % 86400) // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)

        if days > 0:
            return f"{days}d {hours}h {minutes}m {secs}s"
        elif hours > 0:
            return f"{hours}h {minutes}m {secs}s"
        elif minutes > 0:
            return f"{minutes}m {secs}s"
        else:
            return f"{secs}s"

    except Exception:
        return f"{int(seconds)}s"


def get_comprehensive_metrics() -> Dict[str, Any]:
    """
    Get all performance metrics in one call.

    Returns:
        Dictionary containing all performance metrics
    """
    try:
        return {
            "system": get_system_metrics(),
            "process": get_process_metrics(),
            "application": get_application_metrics(),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

    except Exception as e:
        logger.error("Failed to get comprehensive metrics", error=str(e), exc_info=True)
        return {"error": str(e), "timestamp": datetime.now(timezone.utc).isoformat()}


def get_health_status(metrics: Dict[str, Any]) -> str:
    """Determine overall health status based on metrics."""
    try:
        statuses = []

        # Check system metrics
        if "system" in metrics:
            system = metrics["system"]
            statuses.extend(
                [
                    system.get("cpu", {}).get("status", "unknown"),
                    system.get("memory", {}).get("status", "unknown"),
                    system.get("disk", {}).get("status", "unknown"),
                ]
            )

        # Check process metrics
        if "process" in metrics:
            process = metrics["process"]
            statuses.append(process.get("status", "unknown"))

        # Check application metrics
        if "application" in metrics:
            app = metrics["application"]
            statuses.append(app.get("status", "unknown"))

        # Determine overall status
        if "critical" in statuses:
            return "critical"
        elif "warning" in statuses:
            return "warning"
        elif "unknown" in statuses:
            return "degraded"
        elif all(status == "healthy" for status in statuses):
            return "healthy"
        else:
            return "degraded"

    except Exception as e:
        logger.error("Failed to determine health status", error=str(e))
        return "unknown"
