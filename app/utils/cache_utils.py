"""
Cache utilities and key building functions.

Provides consistent cache key generation and cache-related utilities.
"""

import hashlib
from typing import Dict, Any, Optional
from uuid import UUID


def build_user_key(user_id: UUID) -> str:
    return f"user:{user_id}"


def build_session_key(session_id: str) -> str:
    return f"session:{session_id}"


def build_otp_key(identifier: str, purpose: str) -> str:
    """Build cache key for OTP."""
    return f"otp:{purpose}:{identifier}"


def build_token_blacklist_key(token_jti: str) -> str:
    return f"blacklist:token:{token_jti}"


def build_api_response_key(endpoint: str, params_hash: str) -> str:
    return f"api_response:{endpoint}:{params_hash}"


def build_user_permissions_key(user_id: UUID, resource: str) -> str:
    return f"permissions:{user_id}:{resource}"


def build_search_results_key(
    query: str, filters: Optional[Dict[str, Any]] = None
) -> str:
    key_parts = ["search", query]

    if filters:
        filter_str = str(sorted(filters.items()))
        filter_hash = hashlib.md5(filter_str.encode()).hexdigest()[:8]
        key_parts.append(filter_hash)

    return ":".join(key_parts)


def build_pagination_key(base_key: str, page: int, size: int) -> str:
    return f"{base_key}:page:{page}:size:{size}"


def build_user_activity_key(user_id: UUID, activity_type: str) -> str:
    return f"activity:{user_id}:{activity_type}"


def build_notification_key(user_id: UUID, notification_type: str = "all") -> str:
    return f"notifications:{user_id}:{notification_type}"


def build_analytics_key(metric: str, period: str, date: str) -> str:
    return f"analytics:{metric}:{period}:{date}"


def generate_cache_key(*args, **kwargs) -> str:
    """Generate cache key from arguments."""
    key_parts = []

    for arg in args:
        if isinstance(arg, (str, int, float, UUID)):
            key_parts.append(str(arg))
        else:
            key_parts.append(str(hash(str(arg))))

    for key, value in sorted(kwargs.items()):
        key_parts.append(f"{key}:{value}")

    return ":".join(key_parts)


def hash_dict_for_cache(data: Dict[str, Any]) -> str:
    """Create hash of dictionary for cache key."""
    sorted_items = sorted(data.items())
    data_str = str(sorted_items)
    return hashlib.md5(data_str.encode()).hexdigest()[:12]


def get_cache_ttl(
    key: str, cache_type: Optional[str] = None, cache_type_idx: int = 0
) -> int:
    """Get appropriate TTL for different cache types."""
    if cache_type:
        cache_type = cache_type.lower()
    else:
        cache_type = key.split(":")[cache_type_idx]

    ttl_mapping = {
        "token_bucket": 3600,  # 1 hour
        "api_response": 300,  # 5 minutes
        "search": 1800,  # 30 minutes
        "analytics": 86400,  # 24 hours
        "notifications": 300,  # 5 minutes
        "temporary": 60,  # 1 minute
        "long_term": 86400 * 7,  # 1 week
        "database_query": 600,  # 10 minutes
        "auth_token": 900,  # 15 minutes
        "user_permissions": 1800,  # 30 minutes
        "static_data": 86400,  # 24 hours
        "rate_limit": 3600,  # 1 hour
        "blacklist": 86400,  # 24 hours
        "otp": 300,  # 5 minutes
        "password_reset": 900,  # 15 minutes
        "email_verification": 900,  # 15 minutes
        "activity": 86400,  # 24 hours
        "session": 86400,  # 24 hours
        "user": 86400,  # 24 hours
        "permissions": 1800,  # 30 minutes
        "content": 86400,  # 24 hours
        "account": 86400,  # 24 hours
        "role": 86400,  # 24 hours
        "audit": 86400,  # 24 hours
        "logs": 86400,  # 24 hours
        "metrics": 86400,  # 24 hours
        "config": 86400,  # 24 hours
        "system": 86400,  # 24 hours
    }

    return ttl_mapping.get(cache_type, 1800)  # Default 30 minutes
