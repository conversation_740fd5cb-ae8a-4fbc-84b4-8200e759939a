"""
Data formatting utilities for phone numbers, emails, and other data types.

Provides consistent formatting functions for user data display and storage.
"""

from pydantic import EmailStr


def format_phone_number(phone: str) -> str:
    """Format phone number to standard format."""
    # Remove all non-digit characters
    digits_only = "".join(filter(str.isdigit, phone))

    # Handle Indian phone numbers
    if len(digits_only) == 10:
        return f"+91{digits_only}"
    elif len(digits_only) == 12 and digits_only.startswith("91"):
        return f"+{digits_only}"
    elif len(digits_only) == 13 and digits_only.startswith("91"):
        return f"+{digits_only}"

    return phone  # Return original if can't format


def mask_email(email: EmailStr) -> EmailStr:
    """Mask email address for privacy."""
    local, domain = email.split("@", 1)

    if len(local) <= 2:
        masked_local = local
    else:
        masked_local = local[0] + "*" * (len(local) - 2) + local[-1]

    return f"{masked_local}@{domain}"


def mask_phone_number(phone: str) -> str:
    """Mask phone number for privacy."""
    if len(phone) < 4:
        return phone

    return phone[:2] + "*" * (len(phone) - 4) + phone[-2:]


def normalize_email(email: EmailStr) -> EmailStr:
    """Normalize email address for consistent storage and duplicate detection."""
    # Convert to lowercase and strip whitespace
    normalized = email.lower().strip()

    # Split into local and domain parts and remove tags
    local_part, domain = normalized.rsplit("@", 1)
    if "+" in local_part:
        local_part = local_part.split("+")[0]

    # Domain-specific normalization
    if domain in ["gmail.com", "googlemail.com"]:
        local_part = local_part.replace(".", "")  # Remove dots Gmail ignores them
        domain = "gmail.com"  # Normalize googlemail.com to gmail.com

    return f"{local_part}@{domain}"


def format_currency(amount: float, currency: str = "INR") -> str:
    """Format currency amount for display."""
    if currency == "INR":
        # Indian Rupee formatting
        if amount >= 10000000:  # 1 crore
            return f"₹{amount / 10000000:.1f}Cr"
        elif amount >= 100000:  # 1 lakh
            return f"₹{amount / 100000:.1f}L"
        elif amount >= 1000:  # 1 thousand
            return f"₹{amount / 1000:.1f}K"
        else:
            return f"₹{amount:.2f}"
    else:
        # Generic formatting
        return f"{currency} {amount:,.2f}"


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    size = float(size_bytes)

    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1

    return f"{size:.1f} {size_names[i]}"


def format_duration(seconds: int) -> str:
    """Format duration in seconds to human readable format."""
    if seconds < 60:
        return f"{seconds}s"
    elif seconds < 3600:
        minutes = seconds // 60
        remaining_seconds = seconds % 60
        if remaining_seconds > 0:
            return f"{minutes}m {remaining_seconds}s"
        return f"{minutes}m"
    else:
        hours = seconds // 3600
        remaining_minutes = (seconds % 3600) // 60
        if remaining_minutes > 0:
            return f"{hours}h {remaining_minutes}m"
        return f"{hours}h"
