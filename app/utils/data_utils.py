"""
Data processing and manipulation utilities.

Provides functions for data cleaning, transformation, and validation.
"""

import json
from typing import Dict, Any, List


def clean_dict(
    data: Dict[str, Any], remove_none: bool = True, remove_empty: bool = False
) -> Dict[str, Any]:
    """Clean dictionary by removing None or empty values."""
    cleaned = {}

    for key, value in data.items():
        if remove_none and value is None:
            continue
        if remove_empty and not value:
            continue
        cleaned[key] = value

    return cleaned


def deep_merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """Deep merge two dictionaries."""
    result = dict1.copy()

    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value

    return result


def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """Safely parse JSON string with fallback."""
    if not json_str:
        return default

    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(data: Any, default: str = "{}") -> str:
    """Safely serialize data to JSON string with fallback."""
    try:
        return json.dumps(data, default=str, ensure_ascii=False)
    except (TypeError, ValueError):
        return default


def flatten_dict(data: Dict[str, Any], separator: str = ".") -> Dict[str, Any]:
    """Flatten nested dictionary."""

    def _flatten(obj, parent_key="", sep="."):
        items = []
        if isinstance(obj, dict):
            for k, v in obj.items():
                new_key = f"{parent_key}{sep}{k}" if parent_key else k
                items.extend(_flatten(v, new_key, sep=sep).items())
        else:
            return {parent_key: obj}
        return dict(items)

    return _flatten(data, sep=separator)


def unflatten_dict(data: Dict[str, Any], separator: str = ".") -> Dict[str, Any]:
    """Unflatten dictionary with nested keys."""
    result = {}

    for key, value in data.items():
        keys = key.split(separator)
        current = result

        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]

        current[keys[-1]] = value

    return result


def extract_nested_value(
    data: Dict[str, Any], key_path: str, default: Any = None
) -> Any:
    """Extract value from nested dictionary using dot notation."""
    keys = key_path.split(".")
    current = data

    try:
        for key in keys:
            current = current[key]
        return current
    except (KeyError, TypeError):
        return default


def set_nested_value(data: Dict[str, Any], key_path: str, value: Any) -> Dict[str, Any]:
    """Set value in nested dictionary using dot notation."""
    keys = key_path.split(".")
    current = data

    for key in keys[:-1]:
        if key not in current:
            current[key] = {}
        current = current[key]

    current[keys[-1]] = value
    return data


def filter_dict_by_keys(
    data: Dict[str, Any], allowed_keys: List[str]
) -> Dict[str, Any]:
    """Filter dictionary to only include specified keys."""
    return {k: v for k, v in data.items() if k in allowed_keys}


def exclude_dict_keys(data: Dict[str, Any], excluded_keys: List[str]) -> Dict[str, Any]:
    """Exclude specified keys from dictionary."""
    return {k: v for k, v in data.items() if k not in excluded_keys}


def transform_dict_keys(data: Dict[str, Any], transformer) -> Dict[str, Any]:
    """Transform dictionary keys using provided function."""
    return {transformer(k): v for k, v in data.items()}
