"""
Query builder utilities for reducing repository code duplication.

Provides reusable query patterns and builders for common database operations
with consistent error handling and logging.
"""

from typing import Optional, Dict, Any, List, Type, TypeVar
from uuid import UUID
from sqlmodel import SQLModel, Session, select, and_, or_, func, desc, asc
from sqlalchemy.orm import selectinload, noload
from sqlalchemy.exc import SQLAlchemyError

from app.core.logging import get_logger
from app.core.exceptions import DatabaseError
from app.models.common_models import (
    SearchParams,
    PaginationParams,
    PaginatedResponse,
    PaginationMeta,
)

logger = get_logger(__name__)

T = TypeVar("T", bound=SQLModel)


class QueryBuilder:
    """Generic query builder for common database operations."""

    def __init__(self, model_class: Type[T], session: Session):
        self.model_class = model_class
        self.session = session
        self.query = select(model_class)
        self._filters = []
        self._options = []
        self._order_by = []

    def filter_by_id(self, entity_id: UUID) -> "QueryBuilder":
        """Add ID filter to query."""
        self._filters.append(self.model_class.id == entity_id)
        return self

    def filter_by_field(self, field_name: str, value: Any) -> "QueryBuilder":
        """Add field filter to query."""
        field = getattr(self.model_class, field_name)
        self._filters.append(field == value)
        return self

    def filter_by_fields(self, filters: Dict[str, Any]) -> "QueryBuilder":
        """Add multiple field filters to query."""
        for field_name, value in filters.items():
            if value is not None:
                self.filter_by_field(field_name, value)
        return self

    def filter_active_only(self) -> "QueryBuilder":
        """Add active filter if model has is_active field."""
        if hasattr(self.model_class, "is_active"):
            self._filters.append(self.model_class.is_active)
        return self

    def with_relationships(self, *relationships) -> "QueryBuilder":
        """Add relationship loading options."""
        for relationship in relationships:
            self._options.append(selectinload(relationship))
        return self

    def without_relationships(self, *relationships) -> "QueryBuilder":
        """Exclude relationship loading options."""
        for relationship in relationships:
            self._options.append(noload(relationship))
        return self

    def order_by_field(self, field_name: str, desc: bool = False) -> "QueryBuilder":
        """Add ordering to query."""
        field = getattr(self.model_class, field_name)
        if desc:
            self._order_by.append(field.desc())
        else:
            self._order_by.append(field.asc())
        return self

    def build(self) -> select:
        """Build the final query."""
        # Apply filters
        if self._filters:
            self.query = self.query.where(and_(*self._filters))

        # Apply options
        if self._options:
            self.query = self.query.options(*self._options)

        # Apply ordering
        if self._order_by:
            self.query = self.query.order_by(*self._order_by)

        return self.query

    def first(self) -> Optional[T]:
        """Execute query and return first result."""
        try:
            query = self.build()
            return self.session.exec(query).first()
        except SQLAlchemyError as e:
            logger.error(
                "Database error in query execution",
                model=self.model_class.__name__,
                error=str(e),
                exc_info=True,
            )
            raise DatabaseError(
                f"Failed to execute query for {self.model_class.__name__}",
                operation="query_first",
                original_error=str(e),
            ) from e

    def all(self) -> List[T]:
        """Execute query and return all results."""
        try:
            query = self.build()
            return list(self.session.exec(query).all())
        except SQLAlchemyError as e:
            logger.error(
                "Database error in query execution",
                model=self.model_class.__name__,
                error=str(e),
                exc_info=True,
            )
            raise DatabaseError(
                f"Failed to execute query for {self.model_class.__name__}",
                operation="query_all",
                original_error=str(e),
            ) from e

    def count(self) -> int:
        """Execute query and return count."""
        try:
            from sqlmodel import func

            count_query = select(func.count(self.model_class.id))

            # Apply same filters
            if self._filters:
                count_query = count_query.where(and_(*self._filters))

            return self.session.exec(count_query).one()
        except SQLAlchemyError as e:
            logger.error(
                "Database error in count query",
                model=self.model_class.__name__,
                error=str(e),
                exc_info=True,
            )
            raise DatabaseError(
                f"Failed to count {self.model_class.__name__}",
                operation="query_count",
                original_error=str(e),
            ) from e


def build_search_query(base_query, params: SearchParams, model_class):
    """Build a search query with filters and sorting."""
    query = base_query

    # Apply text search if query provided
    if params.query and params.fields:
        search_conditions = []
        for field in params.fields:
            if hasattr(model_class, field):
                column = getattr(model_class, field)
                # Use ILIKE for case-insensitive search in PostgreSQL
                search_conditions.append(column.ilike(f"%{params.query}%"))

        if search_conditions:
            query = query.where(or_(*search_conditions))

    # Apply additional filters
    for field, value in params.filters.items():
        if hasattr(model_class, field):
            column = getattr(model_class, field)
            if isinstance(value, list):
                query = query.where(column.in_(value))
            else:
                query = query.where(column == value)

    # Apply sorting
    if params.sort_by and hasattr(model_class, params.sort_by):
        column = getattr(model_class, params.sort_by)
        if params.sort_order == "desc":
            query = query.order_by(desc(column))
        else:
            query = query.order_by(asc(column))

    return query


def paginate_query(
    session: Session, query, params: PaginationParams, count_query=None
) -> PaginatedResponse:
    """Apply offset-based pagination to a query with optimizations."""
    try:
        # Get total count efficiently
        if count_query is not None:
            total = session.exec(count_query).one()
        else:
            # Use optimized count query
            count_stmt = select(func.count()).select_from(query.subquery())
            total = session.exec(count_stmt).one()

        # Apply pagination
        paginated_query = query.offset(params.offset).limit(params.size)
        items = list(session.exec(paginated_query).all())

        # Calculate metadata efficiently
        pages = (total + params.size - 1) // params.size if params.size > 0 else 0

        meta = PaginationMeta(
            page=params.page,
            size=params.size,
            total=total,
            pages=pages,
            has_next=params.page < pages,
            has_prev=params.page > 1,
        )

        logger.debug(
            "Paginated query executed",
            page=params.page,
            size=params.size,
            total=total,
            items_returned=len(items),
        )

        return PaginatedResponse(items=items, meta=meta)

    except SQLAlchemyError as e:
        logger.error("Database error in pagination", error=str(e), exc_info=True)
        raise DatabaseError(
            "Failed to paginate query",
            operation="paginate_query",
            original_error=str(e),
        ) from e
    except Exception as e:
        logger.error("Unexpected pagination error", error=str(e), exc_info=True)
        raise
