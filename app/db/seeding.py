"""
Database seeding utilities for initial data setup.

Provides functions to create initial admin user and other required data.
"""

from typing import Optional
from sqlmodel import Session, select

from app.core.logging import get_logger
from app.core.config import settings
from app.schemas.user_schema import User, Account, UserRoleEnum
from app.services.auth_service import UserAuthenticationService
from app.db.database import get_session
from app.core.security import get_password_hash

logger = get_logger(__name__)


def create_admin_user(
    session: Session,
    email: str = "<EMAIL>",
    password: str = "Admin123!@#",
    first_name: str = "System",
    last_name: str = "Administrator",
) -> Optional[User]:
    try:
        existing_admin = session.exec(select(User).where(User.email == email)).first()

        if existing_admin:
            logger.info("Admin user already exists", email=email)
            return existing_admin

        hashed_password = get_password_hash(password)

        # Create admin user
        admin_user = User(
            email=email,
            phone_number="+************",
            first_name=first_name,
            last_name=last_name,
            hashed_password=hashed_password,
            role=UserRoleEnum.ADMIN,
            is_active=True,
            is_2fa_enabled=False,
        )

        # Create admin account
        admin_account = Account(
            account_name=f"{first_name} {last_name}",
            account_type="admin",
            is_active=True,
        )

        # Associate account with user
        admin_user.account = admin_account

        # Save to database
        session.add(admin_user)
        session.commit()
        session.refresh(admin_user)

        logger.info(
            "Admin user created successfully",
            user_id=str(admin_user.id),
            email=email,
            role=admin_user.role.value,
        )

        return admin_user

    except Exception as e:
        logger.error("Failed to create admin user", error=str(e), email=email)
        session.rollback()
        raise


def create_sample_users(session: Session) -> list[User]:
    """
    Create sample users for development/testing.

    Args:
        session: Database session

    Returns:
        list[User]: List of created sample users
    """
    if settings.ENVIRONMENT == "production":
        logger.warning("Skipping sample user creation in production")
        return []

    try:
        sample_users = []

        # Sample user data
        users_data = [
            {
                "email": "<EMAIL>",
                "password": "Employee123!",
                "first_name": "John",
                "last_name": "Employee",
                "role": UserRoleEnum.EMPLOYEE,
                "phone": "+919876543210",
            },
            {
                "email": "<EMAIL>",
                "password": "Approver123!",
                "first_name": "Jane",
                "last_name": "Approver",
                "role": UserRoleEnum.APPROVER,
                "phone": "+919876543211",
            },
            {
                "email": "<EMAIL>",
                "password": "Preacher123!",
                "first_name": "David",
                "last_name": "Preacher",
                "role": UserRoleEnum.PREACHER,
                "phone": "+919876543212",
            },
            {
                "email": "<EMAIL>",
                "password": "Volunteer123!",
                "first_name": "Sarah",
                "last_name": "Volunteer",
                "role": UserRoleEnum.VOLUNTEER,
                "phone": "+919876543213",
            },
        ]

        for user_data in users_data:
            # Check if user already exists
            existing_user = session.exec(
                select(User).where(User.email == user_data["email"])
            ).first()

            if existing_user:
                logger.info("Sample user already exists", email=user_data["email"])
                sample_users.append(existing_user)
                continue

            # Create user
            hashed_password = get_password_hash(user_data["password"])

            user = User(
                email=user_data["email"],
                phone_number=user_data["phone"],
                first_name=user_data["first_name"],
                last_name=user_data["last_name"],
                hashed_password=hashed_password,
                role=user_data["role"],
                is_active=True,
                is_2fa_enabled=False,
            )

            # Create account
            account = Account(
                account_name=f"{user_data['first_name']} {user_data['last_name']}",
                account_type="individual",
                is_active=True,
            )

            user.account = account

            session.add(user)
            sample_users.append(user)

            logger.info(
                "Sample user created",
                email=user_data["email"],
                role=user_data["role"].value,
            )

        session.commit()

        # Refresh all users
        for user in sample_users:
            session.refresh(user)

        logger.info(f"Created {len(sample_users)} sample users")
        return sample_users

    except Exception as e:
        logger.error("Failed to create sample users", error=str(e))
        session.rollback()
        raise


def seed_database(create_samples: bool = False) -> None:
    """
    Seed the database with initial data.

    Args:
        create_samples: Whether to create sample users (non-production only)
    """
    try:
        logger.info("Starting database seeding...")

        with next(get_session()) as session:
            # Create admin user
            admin_user = create_admin_user(session)

            # Create sample users if requested and not in production
            if create_samples and settings.ENVIRONMENT != "production":
                sample_users = create_sample_users(session)
                logger.info(
                    f"Database seeding completed: 1 admin + {len(sample_users)} sample users"
                )
            else:
                logger.info("Database seeding completed: 1 admin user")

        logger.info("Database seeding successful")

    except Exception as e:
        logger.error("Database seeding failed", error=str(e), exc_info=True)
        raise


if __name__ == "__main__":
    # Allow running seeding directly
    import sys

    create_samples = "--samples" in sys.argv
    seed_database(create_samples=create_samples)
