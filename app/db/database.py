"""
Database connection, session management, and initialization.

Provides SQLModel database engine, session factory, connection utilities,
initialization, and health checks for PostgreSQL database operations.
"""

import time
from contextlib import contextmanager
from typing import Generator, Dict, Any
from sqlmodel import SQLModel, Session, create_engine, text
from sqlalchemy.pool import StaticPool, QueuePool
from sqlalchemy.exc import OperationalError, DisconnectionError
from sqlalchemy.engine import Engine

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import DatabaseError, ServiceUnavailableError, ValidationError

logger = get_logger("database")


def _create_db_engine() -> Engine:
    """Create and configure the database engine with enhanced connection pooling."""
    try:
        if settings.DATABASE_URL:
            # Production/development with real database
            engine = create_engine(
                settings.DATABASE_URL,
                poolclass=QueuePool,
                pool_size=settings.DATABASE_POOL_SIZE,
                max_overflow=settings.DATABASE_MAX_OVERFLOW,
                pool_pre_ping=True,  # Verify connections before use
                pool_recycle=3600,  # Recycle connections every hour
                pool_timeout=30,  # Timeout for getting connection from pool
                echo=settings.DATABASE_ECHO,  # Log SQL queries if enabled
                connect_args={
                    "connect_timeout": 10,  # Connection timeout
                    "application_name": f"{settings.APP_NAME}_api",
                    "options": "-c timezone=UTC",
                },
            )
            logger.info(
                "Database engine created",
                pool_size=settings.DATABASE_POOL_SIZE,
                max_overflow=settings.DATABASE_MAX_OVERFLOW,
                database_type="postgresql",
            )
        else:
            # Fallback to SQLite for testing/development
            engine = create_engine(
                "sqlite:///./app.db",
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
                echo=settings.DATABASE_ECHO,
            )
            logger.info("Database engine created", database_type="sqlite")

        return engine

    except Exception as e:
        logger.error("Failed to create database engine", error=str(e), exc_info=True)
        raise DatabaseError(
            "Failed to initialize database engine",
            operation="create_engine",
            original_error=str(e),
        )


# Global engine instance
engine = _create_db_engine()


def get_session() -> Generator[Session, None, None]:
    """
    Dependency to get database session with enhanced error handling.

    Yields:
        Session: SQLModel database session

    Usage:
        @app.get("/users/")
        def get_users(session: Session = Depends(get_session)):
            return session.exec(select(User)).all()
    """
    try:
        with Session(engine) as session:
            yield session
    except (OperationalError, DisconnectionError) as e:
        logger.error("Database connection error in session", error=str(e))
        raise ServiceUnavailableError(
            "Database service temporarily unavailable",
            service="database",
            original_error=str(e),
        )
    except Exception as e:
        logger.error(
            "Unexpected error in database session", error=str(e), exc_info=True
        )
        raise DatabaseError(
            "Database session error", operation="get_session", original_error=str(e)
        )


@contextmanager
def get_session_context():
    """
    Get database session context manager for use outside of FastAPI dependency injection.

    Yields:
        Session: SQLModel database session with automatic cleanup

    Usage:
        with get_session_context() as session:
            user = session.get(User, user_id)
            # Session is automatically closed
    """
    session = None
    try:
        session = Session(engine)
        yield session
    except (OperationalError, DisconnectionError) as e:
        logger.error("Database connection error in context session", error=str(e))
        if session:
            session.rollback()
        raise ServiceUnavailableError(
            "Database service temporarily unavailable",
            service="database",
            original_error=str(e),
        )
    except Exception as e:
        logger.error(
            "Unexpected error in database context session", error=str(e), exc_info=True
        )
        if session:
            session.rollback()
        raise DatabaseError(
            "Database session error",
            operation="get_session_context",
            original_error=str(e),
        )
    finally:
        if session:
            session.close()


def check_connection() -> bool:
    """
    Check if database connection is working with enhanced diagnostics.

    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        start_time = time.time()
        with Session(engine) as session:
            # Test basic connectivity
            session.exec(text("SELECT 1"))

            # Test transaction capability
            session.exec(text("SELECT 1"))

        response_time = time.time() - start_time

        if response_time > 1.0:  # Log slow connections
            logger.warning(
                "Slow database connection detected", response_time=response_time
            )

        return True

    except (OperationalError, DisconnectionError) as e:
        logger.warning(
            "Database connection check failed - connection error",
            error=str(e),
            error_type=type(e).__name__,
        )
        return False
    except Exception as e:
        logger.error(
            "Database connection check failed - unexpected error",
            error=str(e),
            error_type=type(e).__name__,
            exc_info=True,
        )
        return False


def get_database_info() -> Dict[str, Any]:
    """
    Get comprehensive database connection information with enhanced metrics.

    Returns:
        dict: Database information including URL, pool status, and performance metrics
    """
    try:
        # Safely get database URL without password
        db_url = str(engine.url)
        if engine.url.password:
            db_url = db_url.replace(engine.url.password, "***")

        # Get comprehensive pool information
        pool_info = {}
        try:
            pool = engine.pool
            pool_info = {
                "pool_size": getattr(pool, "size", lambda: 0)(),
                "checked_out_connections": getattr(pool, "checkedout", lambda: 0)(),
                "overflow_connections": getattr(pool, "overflow", lambda: 0)(),
                "checked_in_connections": getattr(pool, "checkedin", lambda: 0)(),
            }

            # Calculate derived metrics
            total_connections = (
                pool_info["checked_out_connections"]
                + pool_info["checked_in_connections"]
            )
            if pool_info["pool_size"] > 0:
                pool_info["pool_utilization_percent"] = round(
                    (pool_info["checked_out_connections"] / pool_info["pool_size"])
                    * 100,
                    2,
                )
            else:
                pool_info["pool_utilization_percent"] = 0

            pool_info["total_connections"] = total_connections
            pool_info["available_connections"] = (
                pool_info["pool_size"] - pool_info["checked_out_connections"]
            )

            # Health indicators
            pool_info["is_healthy"] = (
                pool_info["checked_out_connections"] < pool_info["pool_size"]
                and pool_info["overflow_connections"] < settings.DATABASE_MAX_OVERFLOW
            )
        except Exception as pool_error:
            logger.warning("Failed to get detailed pool stats", error=str(pool_error))
            # Fallback to basic pool info
            if hasattr(engine.pool, "size"):
                pool_info["pool_size"] = engine.pool.size()
            if hasattr(engine.pool, "checkedout"):
                pool_info["checked_out_connections"] = engine.pool.checkedout()

        # Test connection with timing
        start_time = time.time()
        is_connected = check_connection()
        connection_time = time.time() - start_time

        return {
            "database_url": db_url,
            "is_connected": is_connected,
            "connection_time_ms": round(connection_time * 1000, 2),
            "environment": settings.ENVIRONMENT,
            "database_type": "postgresql" if "postgresql" in db_url else "sqlite",
            **pool_info,
        }

    except Exception as e:
        logger.error("Failed to get database info", error=str(e), exc_info=True)
        return {
            "database_url": "unknown",
            "is_connected": False,
            "environment": settings.ENVIRONMENT,
            "error": str(e),
        }


def init_database(seed_data: bool = True) -> None:
    """
    Initialize the database.

    Creates all tables and performs any necessary setup.
    This function should be called during application startup.

    Args:
        seed_data: Whether to seed initial data (admin user)
    """
    try:
        logger.info("Initializing database...")

        # Create all tables
        logger.info("Creating database tables...")
        SQLModel.metadata.create_all(engine)
        logger.info("Database tables created successfully")

        # Seed initial data if requested
        if seed_data:
            try:
                from app.db.seeding import seed_database
                logger.info("Seeding initial database data...")
                seed_database(create_samples=settings.ENVIRONMENT == "development")
                logger.info("Database seeding completed")
            except Exception as e:
                logger.warning("Database seeding failed, continuing without initial data", error=str(e))

        # Log database info for monitoring
        db_info = get_database_info()
        logger.info(
            "Database initialization completed",
            database_url=db_info["database_url"],
            is_connected=db_info["is_connected"],
        )

        if db_info.get("pool_size"):
            logger.info("Database pool configured", pool_size=db_info["pool_size"])

    except Exception as e:
        logger.error("Database initialization failed", error=str(e), exc_info=True)
        raise


def reset_database() -> None:
    """
    Drop and recreate all database tables.

    WARNING: This will delete all data! Use only for development/testing.
    """
    if settings.ENVIRONMENT == "production":
        raise ValidationError(
            "Cannot reset database in production environment",
            field="environment",
            code="production_reset_forbidden",
        )

    try:
        logger.warning("Resetting database - all data will be lost!")
        SQLModel.metadata.drop_all(engine)
        SQLModel.metadata.create_all(engine)
        logger.info("Database reset completed")

    except Exception as e:
        logger.error("Database reset failed", exception=str(e), exc_info=True)
        raise


def close_db_connections() -> None:
    """
    Close all database connections with enhanced cleanup and monitoring.

    This function should be called during application shutdown to ensure
    all database connections are properly closed and resources are freed.
    """
    try:
        # Get connection info before closing
        db_info = get_database_info()
        active_connections = db_info.get("checked_out_connections", 0)

        if active_connections > 0:
            logger.warning(
                "Closing database connections with active sessions",
                active_connections=active_connections,
            )

        # Dispose of the engine and all connections
        engine.dispose()

        logger.info(
            "Database connections closed successfully",
            previous_active_connections=active_connections,
        )

    except Exception as e:
        logger.error(
            "Error closing database connections",
            error=str(e),
            error_type=type(e).__name__,
            exc_info=True,
        )
        # Still try to dispose even if info gathering failed
        try:
            engine.dispose()
        except Exception as dispose_error:
            logger.error(
                "Failed to dispose database engine",
                error=str(dispose_error),
                exc_info=True,
            )
