"""
Authentication endpoints for user login and token management.

Handles user authentication, token generation, and logout.
"""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException

from sqlmodel import Session


from app.core.dependencies import get_db_session, get_current_user
from app.core.logging import get_logger
from app.core.exceptions import (
    AuthenticationError,
    handle_endpoint_errors,
)
from app.schemas.user_schema import User
from app.models.auth_model import (
    Token,
    RefreshTokenRequest,
    LoginRequest,
    LoginResponse,
)
from app.models.user_model import UserCreate, UserResponse
from app.services.user_service import user_service
from app.core.dependencies import require_admin

logger = get_logger(__name__)
router = APIRouter()


@handle_endpoint_errors("register")
@router.post("/register", response_model=UserResponse)
async def register(
    user_data: UserCreate,
    session: Annotated[Session, Depends(get_db_session)],
    admin_user: Annotated[User, Depends(require_admin)]
) -> UserResponse:
    """Register a new user (admin only)."""
    from app.utils.validators import sanitize_input

    # Sanitize input data
    sanitized_data = sanitize_input(user_data.model_dump())
    sanitized_user_data = UserCreate(**sanitized_data)

    # Create user
    created_user = user_service.create_user(sanitized_user_data, session)

    logger.info(
        "User registered successfully",
        user_id=str(created_user.id),
        email=created_user.email,
        role=created_user.role,
        created_by=str(admin_user.id)
    )

    return created_user

@handle_endpoint_errors("login")
@router.post("/login", response_model=LoginResponse)
async def login(
    login_data: LoginRequest, session: Annotated[Session, Depends(get_db_session)]
) -> LoginResponse:
    """Authenticate user and return access tokens."""
    try:
        
        # Authenticate user
        user = user_service.authenticate_user(
            login_data.email, login_data.password, session
        )

        if not user:
            # Log with masked email for privacy
            logger.warning("Login failed" )
            raise AuthenticationError("Invalid email or password")

        # Create tokens
        tokens = user_service.create_user_tokens(user)

        # Log successful login
        # log_user_operation("login", str(user.id), success=True, email=user.email)

        return LoginResponse(
            access_token=tokens.access_token,
            token_type=tokens.token_type,
            refresh_token=tokens.refresh_token,
            is_2fa_enabled=user.is_2fa_enabled
        )

    except (HTTPException, AuthenticationError):
        raise
    except Exception:
        raise

@handle_endpoint_errors("refresh_token")
@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    session: Annotated[Session, Depends(get_db_session)],
) -> Token:
    """Refresh access token using refresh token."""
    try:
        from app.core.security import (
            verify_token,
            create_access_token,
            create_refresh_token,
        )

        # Verify refresh token
        user_id_str = verify_token(refresh_data.refresh_token)
        if not user_id_str:
            raise AuthenticationError("Invalid refresh token")

        # Get user to ensure they still exist and are active
        from uuid import UUID

        user_id = UUID(user_id_str)
        user_response = user_service.get_user_by_id(user_id, session)

        if not user_response:
            raise AuthenticationError("User not found")

        # Create new tokens
        new_access_token = create_access_token(user_id_str)
        new_refresh_token = create_refresh_token(user_id_str)

        logger.info("Token refreshed successfully", user_id=user_id_str)

        return Token(
            access_token=new_access_token,
            token_type="bearer",
            refresh_token=new_refresh_token,
        )

    except (HTTPException, AuthenticationError):
        raise
    except Exception:
        raise


@handle_endpoint_errors("logout")
@router.post("/logout")
async def logout(current_user: Annotated[User, Depends(get_current_user)]) -> dict:
    """Logout user (invalidate tokens)."""
    try:
        # In a production system, you would:
        # 1. Add the token to a blacklist
        # 2. Update the user's account record
        # 3. Clear any cached user data

        logger.info("User logged out", user_id=str(current_user.id))

        return {"message": "Successfully logged out", "success": True}

    except Exception:
        raise
