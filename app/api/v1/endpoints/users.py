"""
User management endpoints for CRUD operations.

Handles user creation, updates, role management (admin-only operations).
"""

from typing import Annotated, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session
from uuid import UUID

from app.core.dependencies import get_db_session, require_admin, require_admin_or_approver, get_current_user
from app.core.logging import get_logger
from app.core.exceptions import handle_endpoint_errors, NotFoundError, ValidationError
from app.schemas.user_schema import User, UserRoleEnum
from app.models.user_model import UserCreate, UserResponse
from app.services.user_service import user_service

logger = get_logger(__name__)
router = APIRouter()

@handle_endpoint_errors("create_user")
@router.post("/", response_model=UserResponse)
async def create_user(
    user_data: UserCreate,
    session: Annotated[Session, Depends(get_db_session)],
    admin_user: Annotated[User, Depends(require_admin)]
) -> UserResponse:
    """Create a new user (admin only)."""
    from app.utils.validators import sanitize_input
    
    sanitized_data = sanitize_input(user_data.dict())

    sanitized_user_data = UserCreate(**sanitized_data)

    created_user = user_service.create_user(sanitized_user_data, session)

    return created_user

    

