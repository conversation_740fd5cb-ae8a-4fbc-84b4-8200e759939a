# app/api/v1/endpoints/health.py
"""
Health check endpoints for monitoring system status and performance.
"""

from fastapi import APIRouter, Query
from datetime import datetime, timezone
from typing import Optional
from app.db.database import get_database_info
from app.services.cache_service import cache_service
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


@router.get("/health")
async def health_check(
    detailed: Optional[bool] = Query(
        False, description="Include detailed performance metrics"
    ),
):
    """
    Unified health check endpoint for monitoring system status.

    - Basic mode (default): Returns simple service status for load balancers
    - Detailed mode (?detailed=true): Returns comprehensive metrics for monitoring dashboards
    """

    # Check database connectivity
    try:
        db_stats = get_database_info()
        db_healthy = db_stats.get("is_connected", False)
    except Exception as e:
        logger.error("Database health check failed", error=str(e), exc_info=True)
        db_healthy = False
        db_stats = {"error": str(e)}

    # Check cache connectivity
    try:
        cache_stats = await cache_service.get_cache_info()
        cache_healthy = cache_stats.get("is_connected", False)
    except Exception as e:
        logger.error("Cache health check failed", error=str(e), exc_info=True)
        cache_healthy = False
        cache_stats = {"error": str(e)}

    # Determine overall status
    if db_healthy and cache_healthy:
        overall_status = "healthy"
    elif db_healthy or cache_healthy:
        overall_status = "degraded"
    else:
        overall_status = "unhealthy"

    # Basic response for load balancers and simple monitoring
    response = {
        "status": overall_status,
        "version": "1.0.0",
        "services": {
            "database": "healthy" if db_healthy else "unhealthy",
            "cache": "healthy" if cache_healthy else "unhealthy",
        },
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }

    # Add detailed metrics if requested
    if detailed:
        try:
            from app.utils.performance import (
                get_comprehensive_metrics,
                get_health_status,
            )

            perf_metrics = get_comprehensive_metrics()

            # Update overall status based on performance metrics
            perf_status = get_health_status(perf_metrics)
            if perf_status in ["critical", "warning"] and overall_status == "healthy":
                overall_status = "degraded"
                response["status"] = overall_status

            response["performance"] = perf_metrics
            response["service_details"] = {
                "database": db_stats,
                "cache": cache_stats,
            }

        except Exception as e:
            logger.error("Failed to get detailed metrics", error=str(e), exc_info=True)
            response["performance"] = {"error": "Performance metrics unavailable"}
            response["service_details"] = {
                "database": db_stats,
                "cache": cache_stats,
            }

    return response
