"""
Security constants and patterns for application security.

Centralized security configuration including suspicious patterns,
blocked user agents, and security validation rules.
"""

from typing import List, Dict, Any
import os
import re

# API security configuration
API_SECURITY_CONFIG: Dict[str, Any] = {
    "allow_credentials": True,
    "require_https": False,  # Set to True in production
    "cors_origins": ["http://localhost:3000", "http://localhost:8000"],  # Configure based on environment
    "cors_methods": ["GET", "POST", "PUT", "DELETE", "PATCH"],
    "cors_headers": ["Content-Type", "Authorization", "X-Requested-With"],
    "max_request_timeout": 30,  # seconds
    "enable_request_logging": True,
    "enable_response_compression": True,
}

SUSPICIOUS_PATTERNS: List[str] = [
    # Path traversal
    "../",
    "..\\",
    
    # Script injection
    "<script",
    "javascript:",
    "data:",
    "vbscript:",
    
    # Event handlers
    "onload=",
    "onerror=",
    "onclick=",
    
    # Code execution
    "eval(",
    "alert(",
    "document.cookie",
    
    # SQL injection
    "union select",
    "drop table",
    "insert into",
    "delete from",
    
    # Command injection
    "exec(",
    "system(",
    "cmd.exe",
    "/bin/sh",
    
    # System files
    "passwd",
    "/etc/",
]

BLOCKED_USER_AGENTS: List[str] = [
    "sqlmap",
    "nikto",
    "nmap",
    "masscan",
    "zap",
    "burp",
    "acunetix",
    "nessus",
    "openvas",
    "w3af",
]

SECURITY_HEADERS: Dict[str, str] = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Content-Security-Policy": (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline'; "
        "style-src 'self' 'unsafe-inline'; "
        "img-src 'self' data: https:; "
        "font-src 'self'; "
        "connect-src 'self'; "
        "frame-ancestors 'none'"
    ),
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Permissions-Policy": (
        "geolocation=(), "
        "microphone=(), "
        "camera=(), "
        "payment=(), "
        "usb=(), "
        "magnetometer=(), "
        "gyroscope=(), "
        "speaker=()"
    ),
}

RATE_LIMIT_CONFIG: Dict[str, Dict[str, Any]] = {
    "default": {
        "requests": 100,
        "window": 60, # seconds
        "burst": 10,
    },
    "auth": {
        "requests": 5,
        "window": 60,
        "burst": 2,
    },
    "api": {
        "requests": 1000,
        "window": 60,
        "burst": 50,
    },
}

FILE_UPLOAD_CONFIG: Dict[str, Any] = {
    "max_file_size": 10 * 1024 * 1024,  # 10MB
    "allowed_extensions": {
        "image": [".jpg", ".jpeg", ".png", ".gif", ".webp"],
        "document": [".pdf", ".doc", ".docx", ".txt", ".rtf"],
        "spreadsheet": [".xls", ".xlsx", ".csv"],
        "archive": [".zip", ".tar", ".gz"],
    },
    "blocked_extensions": [
        ".exe", ".bat", ".cmd", ".com", ".pif", ".scr", ".vbs", ".js",
        ".jar", ".app", ".deb", ".pkg", ".dmg", ".iso", ".img",
        ".php", ".asp", ".jsp", ".py", ".rb", ".pl", ".sh",
    ],
    "mime_type_validation": True,
    "virus_scan_enabled": False,  # Configure based on environment
}

INPUT_VALIDATION_CONFIG: Dict[str, Any] = {
    "max_string_length": 10000,
    "max_array_length": 1000,
    "max_object_depth": 10,
    "max_request_size": 10 * 1024 * 1024,  # 10MB
    "sanitize_html": True,
    "strip_whitespace": True,
    "normalize_unicode": True,
}

PASSWORD_POLICY: Dict[str, Any] = {
    "min_length": 8,
    "max_length": 128,
    "require_uppercase": True,
    "require_lowercase": True,
    "require_digits": True,
    "require_special_chars": True,
    "special_chars": "!@#$%^&*()_+-=[]{}|;:,.<>?",
    "max_consecutive_chars": 3,
    "prevent_common_passwords": True,
    "prevent_personal_info": True,
}

# Session security configuration
SESSION_CONFIG: Dict[str, Any] = {
    "session_timeout": 3600,  # 1 hour
    "max_concurrent_sessions": 5,
    "secure_cookies": True,
    "httponly_cookies": True,
    "samesite_cookies": "strict",
    "session_regeneration_interval": 1800,  # 30 minutes
}


# Audit logging configuration
AUDIT_CONFIG: Dict[str, Any] = {
    "log_successful_auth": True,
    "log_failed_auth": True,
    "log_privilege_escalation": True,
    "log_data_access": True,
    "log_data_modification": True,
    "log_admin_actions": True,
    "log_security_events": True,
    "retention_days": 90,
}

# Encryption configuration
ENCRYPTION_CONFIG: Dict[str, Any] = {
    "algorithm": "AES-256-GCM",
    "key_rotation_interval": 86400 * 30,  # 30 days
    "hash_algorithm": "bcrypt",
    "hash_rounds": 12,
    "token_expiry": 3600,  # 1 hour
    "refresh_token_expiry": 86400 * 7,  # 7 days
}


def get_security_pattern_regex() -> str:
    """
    Get compiled regex pattern for suspicious content detection.
    
    Returns:
        Regex pattern string for security scanning
    """
    
    # Escape special regex characters in patterns
    escaped_patterns = [re.escape(pattern) for pattern in SUSPICIOUS_PATTERNS]
    
    # Create alternation pattern
    return "|".join(escaped_patterns)


def is_blocked_user_agent(user_agent: str) -> bool:
    if not user_agent:
        return False
    user_agent_lower = user_agent.lower()
    return any(blocked in user_agent_lower for blocked in BLOCKED_USER_AGENTS)


def get_rate_limit_for_endpoint(endpoint_type: str) -> Dict[str, Any]:
    return RATE_LIMIT_CONFIG.get(endpoint_type, RATE_LIMIT_CONFIG["default"])


def is_allowed_file_extension(filename: str, file_type: str = None) -> bool:    
    _, ext = os.path.splitext(filename.lower())
    
    if ext in FILE_UPLOAD_CONFIG["blocked_extensions"]:
        return False
    
    if file_type and file_type in FILE_UPLOAD_CONFIG["allowed_extensions"]:
        return ext in FILE_UPLOAD_CONFIG["allowed_extensions"][file_type]
    
    all_allowed = []
    for extensions in FILE_UPLOAD_CONFIG["allowed_extensions"].values():
        all_allowed.extend(extensions)
    
    return ext in all_allowed


def validate_password_policy(password: str) -> Dict[str, bool]:
    policy = PASSWORD_POLICY
    results = {
        "length_valid": len(password) >= policy["min_length"] and len(password) <= policy["max_length"],
        "has_uppercase": bool(re.search(r"[A-Z]", password)) if policy["require_uppercase"] else True,
        "has_lowercase": bool(re.search(r"[a-z]", password)) if policy["require_lowercase"] else True,
        "has_digits": bool(re.search(r"\d", password)) if policy["require_digits"] else True,
        "has_special": bool(re.search(f"[{re.escape(policy['special_chars'])}]", password)) if policy["require_special_chars"] else True,
        "no_consecutive": not bool(re.search(r"(.)\1{" + str(policy["max_consecutive_chars"]) + ",}", password)),
    }
    
    results["is_valid"] = all(results.values())
    return results
