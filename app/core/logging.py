"""
Centralized logging configuration with structured logging.

Provides production-ready logging with JSON formatting, correlation IDs,
security event logging, and performance monitoring.
"""

import logging
import sys
import json
import traceback
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from contextvars import ContextVar

import structlog
from structlog.stdlib import LoggerFactory

from app.core.config import settings

# Context variables for request correlation
request_id_ctx: ContextVar[Optional[str]] = ContextVar("request_id", default=None)
user_id_ctx: ContextVar[Optional[str]] = ContextVar("user_id", default=None)


class CustomJSONRenderer:
    """Custom JSON renderer for structured logging."""

    def __call__(self, _logger, _method_name, event_dict):
        """Render log entry as JSON with comprehensive metadata."""
        # Add service metadata
        event_dict.update(
            {
                "service": settings.APP_NAME,
                "version": getattr(settings, "APP_VERSION", "0.1.0"),
                "environment": settings.ENVIRONMENT,
                "level": event_dict.get("level", "").upper(),
                "logger": event_dict.get("logger", ""),
            }
        )

        # Add timestamp if not present
        if "timestamp" not in event_dict:
            event_dict["timestamp"] = datetime.now(timezone.utc).isoformat()

        # Add request correlation if available
        if request_id := request_id_ctx.get():
            event_dict["request_id"] = request_id
        if user_id := user_id_ctx.get():
            event_dict["user_id"] = user_id

        # Enhanced exception handling
        if "exc_info" in event_dict and event_dict["exc_info"]:
            exc_type, exc_value, exc_traceback = event_dict["exc_info"]
            event_dict["exception"] = {
                "type": exc_type.__name__ if exc_type else None,
                "message": str(exc_value) if exc_value else None,
                "traceback": "".join(
                    traceback.format_exception(exc_type, exc_value, exc_traceback)
                ),
                "module": getattr(exc_traceback, "tb_frame", {})
                .get("f_globals", {})
                .get("__name__")
                if exc_traceback
                else None,
            }
            del event_dict["exc_info"]

        return json.dumps(event_dict, default=str, ensure_ascii=False)


def configure_logging() -> None:
    """Configure enhanced structured logging for production."""

    # Determine log level
    log_level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)

    # Configure processors based on environment
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]

    # Use custom JSON renderer for production, pretty printing for development
    if settings.DEBUG:
        processors.append(structlog.dev.ConsoleRenderer(colors=True))
    else:
        processors.append(CustomJSONRenderer())

    # Configure structlog
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )

    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=log_level,
    )

    # Configure specific loggers
    _configure_third_party_loggers()


def _configure_third_party_loggers():
    """Configure third-party library loggers."""
    # Reduce noise from third-party libraries
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("redis").setLevel(logging.WARNING)

    # Set specific levels for important loggers
    if not settings.DEBUG:
        logging.getLogger("uvicorn").setLevel(logging.INFO)
        logging.getLogger("fastapi").setLevel(logging.INFO)


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """Get a configured logger instance."""
    return structlog.get_logger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class."""

    @property
    def logger(self) -> structlog.stdlib.BoundLogger:
        """Get logger instance for this class."""
        return get_logger(self.__class__.__name__)


def log_api_request(
    method: str,
    path: str,
    user_id: str = None,
    request_id: str = None,
    correlation_id: str = None,
    **kwargs,
) -> None:
    """Log API request with enhanced structured data and correlation."""
    logger = get_logger("api.request")

    # Extract and sanitize sensitive data
    sanitized_kwargs = sanitize_log_data(kwargs)

    logger.info(
        "API request started",
        event_type="api_request",
        method=method,
        path=path,
        user_id=user_id,
        request_id=request_id,
        correlation_id=correlation_id or request_id,
        timestamp=datetime.now(timezone.utc).isoformat(),
        **sanitized_kwargs,
    )


def log_api_response(
    method: str,
    path: str,
    status_code: int,
    response_time: float,
    user_id: str = None,
    request_id: str = None,
    correlation_id: str = None,
    **kwargs,
) -> None:
    """Log API response with enhanced metrics and correlation."""
    logger = get_logger("api.response")

    # Determine log level based on status code
    if status_code >= 500:
        log_level = "error"
    elif status_code >= 400:
        log_level = "warning"
    else:
        log_level = "info"

    # Sanitize response data
    sanitized_kwargs = sanitize_log_data(kwargs)

    # Add performance classification
    performance_class = _classify_response_time(response_time)

    getattr(logger, log_level)(
        "API response completed",
        event_type="api_response",
        method=method,
        path=path,
        status_code=status_code,
        response_time=response_time,
        performance_class=performance_class,
        user_id=user_id,
        request_id=request_id,
        correlation_id=correlation_id or request_id,
        timestamp=datetime.now(timezone.utc).isoformat(),
        **sanitized_kwargs,
    )


def log_database_operation(
    operation: str,
    entity_name: str,
    record_id: str = None,
    user_id: str = None,
    **kwargs,
) -> None:
    """Log database operations for audit purposes."""
    logger = get_logger("database.operation")
    logger.info(
        "Database operation",
        operation=operation,
        entity_name=entity_name,
        user_id=user_id,
        record_id=record_id,
        **kwargs,
    )


def log_security_event(
    event_type: str,
    user_id: str = None,
    ip_address: str = None,
    user_agent: str = None,
    **kwargs,
) -> None:
    """Log security-related events."""
    logger = get_logger("security.event")
    logger.warning(
        "Security event",
        event_type=event_type,
        user_id=user_id,
        ip_address=ip_address,
        user_agent=user_agent,
        **kwargs,
    )


def log_error(
    error: Exception,
    context: Dict[str, Any] = None,
    user_id: str = None,
    request_id: str = None,
    correlation_id: str = None,
    **kwargs,
) -> None:
    """Log errors with enhanced context and correlation."""
    logger = get_logger("error")

    # Extract error details
    error_details = {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "error_module": getattr(error, "__module__", None),
    }

    # Add stack trace for debugging
    if hasattr(error, "__traceback__"):
        error_details["stack_trace"] = traceback.format_tb(error.__traceback__)

    logger.error(
        "Application error occurred",
        event_type="application_error",
        user_id=user_id,
        request_id=request_id,
        correlation_id=correlation_id or request_id,
        context=sanitize_log_data(context or {}),
        **error_details,
        **sanitize_log_data(kwargs),
        exc_info=True,
    )


def sanitize_log_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Sanitize log data to remove sensitive information."""
    sensitive_keys = {
        "password",
        "token",
        "secret",
        "key",
        "authcredential",
        "authorization",
        "cookie",
        "session",
        "csrf",
        "api_key",
        "access_token",
        "refresh_token",
        "private_key",
    }

    sanitized = {}
    for key, value in data.items():
        key_lower = key.lower()
        if any(sensitive in key_lower for sensitive in sensitive_keys):
            sanitized[key] = "***REDACTED***"
        elif isinstance(value, dict):
            sanitized[key] = sanitize_log_data(value)
        elif isinstance(value, list):
            sanitized[key] = [
                sanitize_log_data(item) if isinstance(item, dict) else item
                for item in value
            ]
        else:
            sanitized[key] = value

    return sanitized


def log_business_event(
    event_type: str,
    description: str,
    user_id: str = None,
    metadata: Dict[str, Any] = None,
    **kwargs,
) -> None:
    """Log business events for analytics and monitoring."""
    logger = get_logger("business.event")

    logger.info(
        "Business event",
        event_type="business_event",
        business_event_type=event_type,
        description=description,
        user_id=user_id,
        metadata=sanitize_log_data(metadata or {}),
        timestamp=datetime.now(timezone.utc).isoformat(),
        **sanitize_log_data(kwargs),
    )


def log_performance_metric(
    metric_name: str,
    value: float,
    unit: str = "seconds",
    context: Dict[str, Any] = None,
    **kwargs,
) -> None:
    """Log performance metrics for monitoring."""
    logger = get_logger("performance.metric")

    logger.info(
        "Performance metric",
        event_type="performance_metric",
        metric_name=metric_name,
        metric_value=value,
        metric_unit=unit,
        context=sanitize_log_data(context or {}),
        timestamp=datetime.now(timezone.utc).isoformat(),
        **sanitize_log_data(kwargs),
    )


# Utility functions for easier logging
def set_request_context(request_id: str, user_id: Optional[str] = None) -> None:
    """Set request context for correlation across logs."""
    request_id_ctx.set(request_id)
    if user_id:
        user_id_ctx.set(user_id)


def clear_request_context() -> None:
    """Clear request context."""
    request_id_ctx.set(None)
    user_id_ctx.set(None)


def _classify_response_time(response_time: float) -> str:
    """Classify response time for performance monitoring."""
    if response_time < 0.1:
        return "fast"
    elif response_time < 0.5:
        return "normal"
    elif response_time < 2.0:
        return "slow"
    else:
        return "very_slow"
