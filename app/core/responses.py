"""
Standardized response utilities for consistent API responses.

Provides a unified response format across all endpoints with automatic
status code determination and flexible metadata support.
"""

from typing import Any, Dict, Optional, List
from datetime import datetime, timezone
from fastapi.responses import JSONResponse

from app.core.logging import get_logger

logger = get_logger(__name__)


def create_response(
    success: bool = True,
    message: str = None,
    data: Any = None,
    error: Any = None,
    meta: Optional[Dict[str, Any]] = None,
    status_code: int = None,
    headers: Optional[Dict[str, str]] = None,
    include_timestamp: bool = True,
    **kwargs,
) -> JSONResponse:
    """
    Universal response builder for all API responses.

    Args:
        success: Whether the operation was successful
        message: Response message (auto-generated if None)
        data: Response data (for success responses)
        error: Error information (for error responses)
        meta: Additional metadata (pagination, etc.)
        status_code: HTTP status code (auto-determined if None)
        headers: Additional HTTP headers
        include_timestamp: Whether to include timestamp (default: True)
        **kwargs: Additional fields to include in response

    Returns:
        JSONResponse with standardized format
    """
    if status_code is None:
        status_code = 200 if success else (400 if error else 500)

    if message is None:
        message = "Operation successful" if success else "Operation failed"

    # Build response content
    content = {"success": success, "message": message}

    # Add data for success responses
    if success and data is not None:
        content["data"] = data

    # Add error for error responses
    if not success and error is not None:
        content["error"] = error

    # Add metadata
    if meta:
        content["meta"] = meta

    # Add timestamp by default
    if include_timestamp:
        content["timestamp"] = datetime.now(timezone.utc).isoformat()

    # Add any additional fields
    content.update(kwargs)

    return JSONResponse(status_code=status_code, content=content, headers=headers or {})


def create_success_response(
    data: Any = None,
    message: str = "Operation successful",
    meta: Optional[Dict[str, Any]] = None,
    status_code: int = 200,
    **kwargs,
) -> JSONResponse:
    """Create standardized success response."""
    return create_response(
        success=True,
        message=message,
        data=data,
        meta=meta,
        status_code=status_code,
        **kwargs,
    )


def create_error_response(
    message: str,
    error_type: str = "error",
    details: Optional[Dict[str, Any]] = None,
    status_code: int = 400,
    **kwargs,
) -> JSONResponse:
    """Create standardized error response."""
    error_data = {"type": error_type, "message": message}
    if details:
        error_data["details"] = details

    return create_response(
        success=False,
        message=message,
        error=error_data,
        status_code=status_code,
        **kwargs,
    )

def create_paginated_response(
    items: List[Any],
    total: int,
    page: int,
    size: int,
    message: str = "Data retrieved successfully",
    **kwargs,
) -> JSONResponse:
    """Create standardized paginated response."""
    total_pages = (total + size - 1) // size if size > 0 else 0

    pagination_meta = {
        "page": page,
        "size": size,
        "total": total,
        "total_pages": total_pages,
        "has_next": page < total_pages,
        "has_prev": page > 1,
    }

    return create_success_response(
        data=items,
        message=message,
        meta={"pagination": pagination_meta},
        **kwargs,
    )
