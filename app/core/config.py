"""
Application Configuration

Centralized configuration management using Pydantic settings.
Loads configuration from environment variables with proper validation.
"""

import os
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    APP_NAME: str = os.getenv("APP_NAME", "FastAPI Backend")
    API_VERSION: str = "0.1.0"
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")  # Options: development, testing, production
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    LOG_LEVEL: str = "INFO" # Options: DEBUG, INFO, WARNING, ERROR, CRITICAL

    HOST: str = "0.0.0.0"
    PORT: int = 8000

    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./app.db")
    DATABASE_ECHO: bool = False
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20

    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    REDIS_PASSWORD: str = os.getenv("REDIS_PASSWORD", "redis_password")

    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-super-secret-key")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", 30)
    REFRESH_TOKEN_EXPIRE_MINUTES: int = os.getenv("REFRESH_TOKEN_EXPIRE_MINUTES", 1440)
    ALGORITHM: str = os.getenv("ALGORITHM", "HS256")

    SMTP_TLS: bool = True
    SMTP_PORT: int = 587
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_USER: str = "<EMAIL>"
    SMTP_PASSWORD: str = "your-email-password"
    EMAILS_FROM_EMAIL: str = "<EMAIL>"
    EMAILS_FROM_NAME: str = "FastAPI Backend"

    class Config:
        env_file = ".env"
        case_sensitive = True


# Create global settings instance
settings = Settings()
