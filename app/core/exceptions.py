"""
Shared exception handling utilities and custom exceptions.

Provides consistent error handling patterns across the application with
standardized error responses, comprehensive logging, and security considerations.
"""

import functools
import inspect
import asyncio
from typing import Any, Dict, Optional, Callable

from fastapi import HTTPException, status
from sqlalchemy.exc import DatabaseError as SQLAlchemyDatabaseError
from redis.exceptions import RedisError

from app.core.logging import get_logger

logger = get_logger(__name__)


class BaseAPIException(Exception):
    """
    Base exception for API-specific errors.

    Provides a standardized way to handle errors across the application
    with consistent error responses, proper logging, and security considerations.
    """

    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_type: str = "internal_error",
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None,
        **kwargs,
    ):
        self.message = message
        self.status_code = status_code
        self.error_type = error_type
        self.details = details or {}
        self.cause = cause

        # Add any additional context from kwargs
        if kwargs:
            self.details.update(kwargs)

        # Support exception chaining
        super().__init__(self.message)
        if cause:
            self.__cause__ = cause

    def get_log_context(
        self, request_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Get sanitized context for logging."""
        from app.core.logging import sanitize_log_data

        context = {
            "error_type": self.error_type,
            "status_code": self.status_code,
            "exception_class": self.__class__.__name__,
            "details": sanitize_log_data(self.details),
        }

        if request_context:
            context.update(sanitize_log_data(request_context))

        return context


class ValidationError(BaseAPIException):
    """Input validation error exception."""

    def __init__(
        self,
        message: str = "Validation failed",
        field: str = None,
        code: str = None,
        **kwargs,
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            error_type="validation_error",
            field=field,
            code=code,
            **kwargs,
        )


class AuthenticationError(BaseAPIException):
    """Authentication error exception."""

    def __init__(self, message: str = "Authentication failed", **kwargs):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_type="authentication_error",
            **kwargs,
        )


class AuthorizationError(BaseAPIException):
    """Authorization error exception."""

    def __init__(
        self,
        message: str = "Insufficient permissions",
        required_permission: str = None,
        **kwargs,
    ):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            error_type="authorization_error",
            required_permission=required_permission,
            **kwargs,
        )


class NotFoundError(BaseAPIException):
    """Resource not found exception."""

    def __init__(self, resource: str, identifier: str = None, **kwargs):
        message = f"{resource} not found"
        if identifier:
            message += f" with identifier: {identifier}"

        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            error_type="not_found",
            resource=resource,
            identifier=identifier,
            **kwargs,
        )


class ConflictError(BaseAPIException):
    """Resource conflict exception."""

    def __init__(self, message: str, resource: str = None, **kwargs):
        super().__init__(
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            error_type="conflict_error",
            resource=resource,
            **kwargs,
        )


class BusinessLogicError(BaseAPIException):
    """Business logic validation error exception."""

    def __init__(self, message: str, code: str = None, **kwargs):
        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            error_type="business_logic_error",
            code=code,
            **kwargs,
        )


class DatabaseError(BaseAPIException):
    """Database operation error exception."""

    def __init__(self, message: str, operation: str = None, **kwargs):
        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="database_error",
            operation=operation,
            **kwargs,
        )


class ServiceUnavailableError(BaseAPIException):
    """Service unavailable error exception."""

    def __init__(self, message: str, service: str = None, **kwargs):
        super().__init__(
            message=message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            error_type="service_unavailable",
            service=service,
            **kwargs,
        )


def handle_endpoint_errors(
    default_message: str = "An unexpected error occurred",
    log_level: Literal["debug", "info", "warning", "error"] = "error",
    log_context: Optional[Dict[str, Any]] = None] = None
):
    """Decorator for consistent endpoint error handling."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                if asyncio.iscoroutinefunction(func):
                    return asyncio.run(func(*args, **kwargs))
                else:
                    return func(*args, **kwargs)
            except BaseAPIException as e:
                raise HTTPException(status_code=e.status_code, detail=e.message)
            except Exception as e:
                api_exception = convert_exception(e, "Endpoint operation failed")
                logger.error(
                    "Unexpected error in endpoint",
                    params=params,
                    endpoint=func.__name__,
                    error=str(e),
                    exc_info=True,
                )
                raise HTTPException(
                    status_code=api_exception.status_code, detail=api_exception.message
                )
        return wrapper
    return decorator


def handle_service_errors(func: Callable) -> Callable:
    """Decorator for consistent service error handling."""

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            if asyncio.iscoroutinefunction(func):
                return asyncio.run(func(*args, **kwargs))
            else:
                return func(*args, **kwargs)
        except BaseAPIException:
            raise
        except Exception as e:
            api_exception = convert_exception(e, f"{func.__name__} failed")
            api_exception.details.update(
                {"operation": func.__name__, "original_error": str(e)}
            )
            logger.error(
                "Unexpected error in service",
                service=func.__name__,
                error=str(e),
                exc_info=True,
            )
            raise api_exception

    return wrapper


def handle_database_exceptions(func: Callable) -> Callable:
    """Decorator to handle common database exceptions."""

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            if asyncio.iscoroutinefunction(func):
                return asyncio.run(func(*args, **kwargs))
            else:
                return func(*args, **kwargs)
        except BaseAPIException:
            raise
        except Exception as e:
            api_exception = convert_exception(e, "Database operation failed")
            api_exception.details.update(
                {"operation": func.__name__, "original_error": str(e)}
            )
            logger.error(
                "Unexpected error in database operation",
                operation=func.__name__,
                error=str(e),
                exc_info=True,
            )
            raise api_exception

    return wrapper


def handle_cache_exceptions(func: Callable) -> Callable:
    """Decorator to handle cache-related exceptions.
    
    If an exception occurs, it will return a default value based on the return type annotation.
    If no return type annotation is present, it will return None.
    """

    @functools.wraps(func)
    async def wrapper(*args: Any, **kwargs: Any) -> Any:
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(
                "Unexpected error in cache operation",
                cache_operation=func.__name__,
                error=str(e),
                exc_info=True,
            )

            # Determine the return type and return a default value
            return_type = inspect.signature(func).return_annotation

            default_values = {
                bool: False,
                int: 0,
                list: [],
                dict: {},
                str: "",
            }
            return default_values.get(return_type, None)

    return wrapper


def safe_execute(
    operation: Callable,
    fallback_value: Any = None,
    log_errors: bool = True,
    operation_name: Optional[str] = None,
    **kwargs,
) -> Any:
    """
    Safely execute an operation with fallback value on error.

    Automatically detects sync/async functions and handles appropriately.

    Args:
        operation: Function to execute
        fallback_value: Value to return if operation fails
        log_errors: Whether to log errors
        operation_name: Name for logging purposes
        **kwargs: Additional context for logging

    Returns:
        Operation result or fallback value
    """

    import asyncio

    try:
        if asyncio.iscoroutinefunction(operation):
            return asyncio.run(operation())
        else:
            return operation()
    except Exception as e:
        if log_errors:
            op_name = operation_name or getattr(
                operation, "__name__", "unknown_operation"
            )
            logger.warning(
                f"Safe execution failed for {op_name}: {e}",
                operation=op_name,
                error=str(e),
                **kwargs,
            )
        return fallback_value


# Error mapping for automatic conversion
ERROR_MAPPING = {
    ValueError: ValidationError,
    PermissionError: AuthorizationError,
    FileNotFoundError: NotFoundError,
    ConnectionError: ServiceUnavailableError,
    KeyError: NotFoundError,
    AttributeError: ValidationError,
    HTTPException: BaseAPIException,
    Exception: BaseAPIException,
}


def convert_exception(
    error: Exception, default_message: str = "An error occurred"
) -> BaseAPIException:
    """
    Convert any exception to an appropriate BaseAPIException.

    Args:
        error: Original exception to convert
        default_message: Message to use if error message is empty

    Returns:
        BaseAPIException instance with appropriate type and message
    """
    if isinstance(error, BaseAPIException):
        return error

    message = str(error) if str(error) else default_message

    if isinstance(error, SQLAlchemyDatabaseError):
        return DatabaseError(message)

    if isinstance(error, RedisError):
        return ServiceUnavailableError(message, service="cache")

    exception_class = ERROR_MAPPING.get(type(error), BaseAPIException)

    if exception_class == ValidationError:
        return ValidationError(message)
    elif exception_class == NotFoundError:
        return NotFoundError("Resource", "unknown")
    elif exception_class == AuthenticationError:
        return AuthenticationError(message)
    elif exception_class == ConflictError:
        return ConflictError(message)
    elif exception_class == AuthorizationError:
        return AuthorizationError(message)
    elif exception_class == BaseAPIException:
        return BaseAPIException(message)
    else:
        return ServiceUnavailableError(message)
