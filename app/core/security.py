"""Security utilities for authentication and authorization."""

import secrets
import string
import hashlib
import base64
from datetime import datetime, timedelta, timezone
from typing import Optional, Union, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi.security import OAuth2<PERSON>asswordBearer
from cryptography.fernet import <PERSON><PERSON><PERSON>

from app.core.config import settings
from app.core.logging import get_logger
from app.core.exceptions import ValidationError
from app.core.security_constants import PASSWORD_POLICY, ENCRYPTION_CONFIG

logger = get_logger(__name__)

pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__rounds=ENCRYPTION_CONFIG["hash_rounds"],
    bcrypt__ident="2b",
)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")

_token_blacklist = set()

# Password policy settings from centralized configuration
PASSWORD_MIN_LENGTH = PASSWORD_POLICY["min_length"]
PASSWORD_REQUIRE_UPPERCASE = PASSWORD_POLICY["require_uppercase"]
PASSWORD_REQUIRE_LOWERCASE = PASSWORD_POLICY["require_lowercase"]
PASSWORD_REQUIRE_DIGITS = PASSWORD_POLICY["require_digits"]
PASSWORD_REQUIRE_SPECIAL = PASSWORD_POLICY["require_special_chars"]
PASSWORD_SPECIAL_CHARS = PASSWORD_POLICY["special_chars"]


# =============================================================================
# PASSWORD OPERATIONS
# =============================================================================


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a plain password against a hashed password."""
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.warning(f"Password verification error: {e}")
        return False


def get_password_hash(password: str) -> str:
    """Hash a password using bcrypt."""
    validation_result = _validate_password_strength(password)
    if not validation_result["is_valid"]:
        raise ValidationError(
            f"Password does not meet security requirements: {', '.join(validation_result['errors'])}",
            field="password",
            code="weak_password"
        )

    return pwd_context.hash(password)


# =============================================================================
# JWT TOKEN OPERATIONS
# =============================================================================


def create_access_token(
    subject: Union[str, int], expires_delta: Optional[timedelta] = None
) -> str:
    """Create a JWT access token."""
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    to_encode = {"exp": expire, "sub": str(subject)}
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def create_refresh_token(subject: Union[str, int]) -> str:
    """Create a JWT refresh token."""
    expire = datetime.now(timezone.utc) + timedelta(
        minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES
    )
    to_encode = {"exp": expire, "sub": str(subject), "type": "refresh"}
    encoded_jwt = jwt.encode(
        to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def verify_token(token: str) -> Optional[str]:
    """Verify and decode a JWT token."""
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        subject: str = payload.get("sub")
        if subject is None:
            return None
        return subject
    except JWTError:
        return None


def blacklist_token(jti: str) -> bool:
    """Add token to blacklist."""
    try:
        _token_blacklist.add(jti)
        logger.info(f"Token blacklisted: {jti}")
        return True
    except Exception as e:
        logger.error(f"Failed to blacklist token {jti}: {e}")
        return False


def is_token_blacklisted(jti: str) -> bool:
    """Check if token is blacklisted."""
    return jti in _token_blacklist


def generate_password_reset_token(email: str) -> str:
    """Generate a password reset token."""
    expire = datetime.now(timezone.utc) + timedelta(seconds=ENCRYPTION_CONFIG["token_expiry"])
    to_encode = {"exp": expire, "sub": email, "type": "password_reset"}
    encoded_jwt = jwt.encode(
        to_encode,
        settings.SECRET_KEY,
        algorithm=settings.ALGORITHM,
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> Optional[str]:
    """Verify a password reset token."""
    try:
        decoded_token = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        if decoded_token.get("type") != "password_reset":
            return None
        return decoded_token["sub"]
    except JWTError:
        return None


# =============================================================================
# SECURE TOKEN GENERATION
# =============================================================================


def generate_random_string(length: int = 32, include_symbols: bool = False) -> str:
    """Generate cryptographically secure random string."""
    if length <= 0:
        raise ValidationError("Length must be positive", field="length", code="invalid_length")

    characters = string.ascii_letters + string.digits
    if include_symbols:
        characters += "!@#$%^&*"

    try:
        return "".join(secrets.choice(characters) for _ in range(length))
    except Exception as e:
        logger.error(f"Failed to generate random string: {e}")
        raise


def generate_otp(length: int = 6) -> str:
    """Generate numeric OTP."""
    if length <= 0 or length > 10:
        raise ValidationError("OTP length must be between 1 and 10", field="length", code="invalid_otp_length")

    try:
        return "".join(secrets.choice(string.digits) for _ in range(length))
    except Exception as e:
        logger.error(f"Failed to generate OTP: {e}")
        raise


def generate_secure_token(length: int = 32) -> str:
    """Generate secure token for password reset, email verification, etc."""
    if length <= 0:
        raise ValidationError("Token length must be positive", field="length", code="invalid_token_length")

    try:
        return secrets.token_urlsafe(length)
    except Exception as e:
        logger.error(f"Failed to generate secure token: {e}")
        raise


def generate_api_key(prefix: str = "sk", length: int = 32) -> str:
    """Generate API key with prefix."""
    random_part = secrets.token_urlsafe(length)
    return f"{prefix}_{random_part}"


# =============================================================================
# ENCRYPTION AND HASHING
# =============================================================================


def hash_string(value: str, salt: Optional[str] = None) -> str:
    """Hash string with optional salt using SHA-256."""
    if not value:
        raise ValidationError("Value cannot be empty", field="value", code="empty_value")

    try:
        if salt is None:
            salt = secrets.token_hex(16)

        combined = f"{value}{salt}"
        hashed = hashlib.sha256(combined.encode("utf-8")).hexdigest()
        return f"{salt}:{hashed}"
    except Exception as e:
        logger.error(f"Failed to hash string: {e}")
        raise


def verify_hash(value: str, hashed_value: str) -> bool:
    """Verify hashed string against original value."""
    if not value or not hashed_value:
        return False

    try:
        salt, hash_part = hashed_value.split(":", 1)
        combined = f"{value}{salt}"
        computed_hash = hashlib.sha256(combined.encode("utf-8")).hexdigest()
        return secrets.compare_digest(hash_part, computed_hash)
    except (ValueError, AttributeError) as e:
        logger.warning(f"Hash verification failed: {e}")
        return False


def encrypt_data(data: str, key: Optional[str] = None) -> str:
    """Encrypt sensitive data using Fernet symmetric encryption."""
    if not data:
        raise ValidationError("Data cannot be empty", field="data", code="empty_data")

    try:
        if key is None:
            key = settings.SECRET_KEY

        fernet_key = base64.urlsafe_b64encode(key.encode("utf-8").ljust(32)[:32])
        fernet = Fernet(fernet_key)

        encrypted_data = fernet.encrypt(data.encode("utf-8"))
        return base64.urlsafe_b64encode(encrypted_data).decode("utf-8")
    except Exception as e:
        logger.error(f"Failed to encrypt data: {e}")
        raise


def decrypt_data(encrypted_data: str, key: Optional[str] = None) -> str:
    """Decrypt sensitive data using Fernet symmetric encryption."""
    if not encrypted_data:
        raise ValidationError("Encrypted data cannot be empty", field="encrypted_data", code="empty_encrypted_data")

    try:
        if key is None:
            key = settings.SECRET_KEY

        fernet_key = base64.urlsafe_b64encode(key.encode("utf-8").ljust(32)[:32])
        fernet = Fernet(fernet_key)

        decoded_data = base64.urlsafe_b64decode(encrypted_data.encode("utf-8"))
        decrypted_data = fernet.decrypt(decoded_data)
        return decrypted_data.decode("utf-8")
    except Exception as e:
        logger.error(f"Failed to decrypt data: {e}")
        raise


# =============================================================================
# PRIVATE HELPER FUNCTIONS
# =============================================================================


def _validate_password_strength(password: str) -> Dict[str, Any]:
    """Validate password strength according to security policy."""
    errors = []

    if len(password) < PASSWORD_MIN_LENGTH:
        errors.append(
            f"Password must be at least {PASSWORD_MIN_LENGTH} characters long"
        )

    if PASSWORD_REQUIRE_UPPERCASE and not any(c.isupper() for c in password):
        errors.append("Password must contain at least one uppercase letter")

    if PASSWORD_REQUIRE_LOWERCASE and not any(c.islower() for c in password):
        errors.append("Password must contain at least one lowercase letter")

    if PASSWORD_REQUIRE_DIGITS and not any(c.isdigit() for c in password):
        errors.append("Password must contain at least one digit")

    if PASSWORD_REQUIRE_SPECIAL and not any(
        c in PASSWORD_SPECIAL_CHARS for c in password
    ):
        errors.append(
            f"Password must contain at least one special character: {PASSWORD_SPECIAL_CHARS}"
        )

    weak_passwords = ["password", "123456", "qwerty", "admin", "letmein"]
    if password.lower() in weak_passwords:
        errors.append("Password is too common and easily guessable")

    return {
        "is_valid": len(errors) == 0,
        "errors": errors,
        "strength_score": _calculate_password_strength(password),
    }


def _calculate_password_strength(password: str) -> int:
    """Calculate password strength score (0-100)."""
    score = 0

    score += min(25, len(password) * 2)

    if any(c.isupper() for c in password):
        score += 15
    if any(c.islower() for c in password):
        score += 15
    if any(c.isdigit() for c in password):
        score += 15
    if any(c in PASSWORD_SPECIAL_CHARS for c in password):
        score += 20

    unique_chars = len(set(password))
    if unique_chars < len(password) * 0.7:
        score -= 10

    return min(100, max(0, score))
