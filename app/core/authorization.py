"""
Authorization module for role-based and permission-based access control.

Provides centralized authorization logic, role hierarchy management,
and reusable authorization decorators.
"""

import functools
from typing import Callable, Set
from enum import Enum

from app.schemas.user_schema import User, UserRoleEnum
from app.core.logging import get_logger
from app.core.exceptions import AuthorizationError

logger = get_logger(__name__)


class Permission(str, Enum):
    """System permissions for fine-grained access control."""

    # User management permissions
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_LIST = "user:list"

    # Role management permissions
    ROLE_ASSIGN = "role:assign"
    ROLE_REVOKE = "role:revoke"

    # Account management permissions
    ACCOUNT_ACTIVATE = "account:activate"
    ACCOUNT_DEACTIVATE = "account:deactivate"
    ACCOUNT_RESET_PASSWORD = "account:reset_password"

    # System administration permissions
    SYSTEM_CONFIG = "system:config"
    SYSTEM_LOGS = "system:logs"
    SYSTEM_METRICS = "system:metrics"

    # Content management permissions
    CONTENT_CREATE = "content:create"
    CONTENT_UPDATE = "content:update"
    CONTENT_DELETE = "content:delete"
    CONTENT_PUBLISH = "content:publish"

    class config:
        case_insensitive = True
        use_enum_values = True


class RoleHierarchy:
    """Manages role hierarchy and permission mappings."""

    # Role-based permissions mapping
    ROLE_PERMISSIONS = {
        UserRoleEnum.ADMIN: {
            # Full system access
            Permission.USER_CREATE,
            Permission.USER_READ,
            Permission.USER_UPDATE,
            Permission.USER_DELETE,
            Permission.USER_LIST,
            Permission.ROLE_ASSIGN,
            Permission.ROLE_REVOKE,
            Permission.ACCOUNT_ACTIVATE,
            Permission.ACCOUNT_DEACTIVATE,
            Permission.ACCOUNT_RESET_PASSWORD,
            Permission.SYSTEM_CONFIG,
            Permission.SYSTEM_LOGS,
            Permission.SYSTEM_METRICS,
            Permission.CONTENT_CREATE,
            Permission.CONTENT_UPDATE,
            Permission.CONTENT_DELETE,
            Permission.CONTENT_PUBLISH,
        },
        UserRoleEnum.APPROVER: {
            # User and content management
            Permission.USER_READ,
            Permission.USER_LIST,
            Permission.ACCOUNT_ACTIVATE,
            Permission.ACCOUNT_DEACTIVATE,
            Permission.CONTENT_CREATE,
            Permission.CONTENT_UPDATE,
            Permission.CONTENT_DELETE,
            Permission.CONTENT_PUBLISH,
        },
        UserRoleEnum.EMPLOYEE: {
            # Content management and user viewing
            Permission.USER_READ,
            Permission.USER_LIST,
            Permission.CONTENT_CREATE,
            Permission.CONTENT_UPDATE,
            Permission.CONTENT_DELETE,
        },
        UserRoleEnum.PREACHER: {
            # Content creation and viewing
            Permission.USER_READ,
            Permission.CONTENT_CREATE,
            Permission.CONTENT_UPDATE,
        },
        UserRoleEnum.VOLUNTEER: {
            # Basic read access
            Permission.USER_READ,
            Permission.CONTENT_CREATE,
        },
    }

    @classmethod
    def get_role_permissions(cls, role: UserRoleEnum) -> Set[Permission]:
        """Get all permissions for a role."""
        return cls.ROLE_PERMISSIONS.get(role, set())

    @classmethod
    def has_permission(cls, user_role: UserRoleEnum, permission: Permission) -> bool:
        """Check if role has specific permission."""
        role_permissions = cls.get_role_permissions(user_role)
        return permission in role_permissions


class AuthorizationPolicy:
    """Policy-based authorization for complex access control scenarios."""

    def __init__(self):
        self.role_hierarchy = RoleHierarchy()

    def can_access_user_data(self, current_user: User, target_user_id: str) -> bool:
        """Check if user can access another user's data."""
        # Users can always access their own data
        if str(current_user.id) == target_user_id:
            return True

        # Admins and approvers can access any user data
        if current_user.role in [UserRoleEnum.ADMIN, UserRoleEnum.APPROVER]:
            return True

        return False

    def can_modify_user(self, current_user: User, target_user: User) -> bool:
        """Check if user can modify another user."""
        # Users cannot modify themselves (except through profile endpoints)
        if current_user.id == target_user.id:
            return False

        # Only admins can modify other users
        if current_user.role != UserRoleEnum.ADMIN:
            return False

        # Admins cannot modify other admins (prevent privilege escalation)
        if target_user.role == UserRoleEnum.ADMIN:
            return False

        return True

    def can_assign_role(self, current_user: User, target_role: UserRoleEnum) -> bool:
        """Check if user can assign a specific role."""
        # Only admins can assign roles
        if current_user.role != UserRoleEnum.ADMIN:
            return False

        # Cannot assign admin role (must be done through special process)
        if target_role == UserRoleEnum.ADMIN:
            return False

        return True

    def can_access_resource(self, user: User, resource: str, action: str) -> bool:
        """Generic resource access check."""
        # Build permission string
        permission_str = f"{resource}:{action}"

        try:
            permission = Permission(permission_str)
            return self.role_hierarchy.has_permission(user.role, permission)
        except ValueError:
            # Unknown permission, deny access
            logger.warning(
                "Unknown permission requested",
                user_id=str(user.id),
                permission=permission_str,
            )
            return False


# Global authorization policy instance
auth_policy = AuthorizationPolicy()


def require_role(required_role: UserRoleEnum):
    """Decorator to require specific role or higher."""

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_user = None
            for arg in args:
                if isinstance(arg, User):
                    current_user = arg
                    break

            if not current_user:
                current_user = kwargs.get("current_user")

            if not current_user:
                raise AuthorizationError("User context not found")

            if current_user.role != required_role:
                logger.warning(
                    "Role access denied",
                    user_id=str(current_user.id),
                    user_role=current_user.role.value,
                    required_role=required_role.value,
                    function=func.__name__,
                )
                raise AuthorizationError(f"Role {required_role} required")
            
            return func(*args, **kwargs)

        return wrapper

    return decorator


def require_permission(permission: Permission):
    """Decorator to require specific permission."""

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_user = None
            for arg in args:
                if isinstance(arg, User):
                    current_user = arg
                    break

            if not current_user:
                current_user = kwargs.get("current_user")

            if not current_user:
                raise AuthorizationError("User context not found")

            if not RoleHierarchy.has_permission(current_user.role, permission):
                logger.warning(
                    "Permission denied",
                    user_id=str(current_user.id),
                    user_role=current_user.role.value,
                    required_permission=permission.value,
                    function=func.__name__,
                )
                raise AuthorizationError(f"Permission {permission.value} required")

            return func(*args, **kwargs)

        return wrapper

    return decorator


def require_self_or_admin(target_user_id_param: str = "user_id"):
    """Decorator to require user to be accessing their own data or be an admin."""

    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Extract current_user and target_user_id
            current_user = None
            for arg in args:
                if isinstance(arg, User):
                    current_user = arg
                    break

            if not current_user:
                current_user = kwargs.get("current_user")

            if not current_user:
                raise AuthorizationError("User context not found")

            # Get target user ID from parameters
            target_user_id = kwargs.get(target_user_id_param)
            if not target_user_id:
                # Try to find in positional args (common pattern)
                if len(args) > 1:
                    target_user_id = str(args[1])  # Assuming second arg is user_id

            if not target_user_id:
                raise AuthorizationError("Target user ID not found")

            if not auth_policy.can_access_user_data(current_user, str(target_user_id)):
                logger.warning(
                    "User data access denied",
                    user_id=str(current_user.id),
                    target_user_id=str(target_user_id),
                    function=func.__name__,
                )
                raise AuthorizationError(
                    "Access denied: can only access own data or admin required"
                )

            return func(*args, **kwargs)

        return wrapper

    return decorator


def log_authorization_event(
    event_type: str,
    user: User,
    resource: str = None,
    action: str = None,
    success: bool = True,
    **additional_context,
):
    """Log authorization events for audit purposes."""
    context = {
        "event_type": event_type,
        "user_id": str(user.id),
        "user_role": user.role.value,
        "success": success,
        **additional_context,
    }

    if resource:
        context["resource"] = resource
    if action:
        context["action"] = action

    if success:
        logger.info("Authorization event", **context)
    else:
        logger.warning("Authorization denied", **context)
