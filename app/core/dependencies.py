"""
Core dependencies for FastAPI dependency injection.

Provides authentication, authorization, and database session dependencies.
"""

from typing import Op<PERSON>, Annotated, Generator
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlmodel import Session
from uuid import UUID

from app.core.security import verify_token
from app.core.logging import get_logger
from app.core.exceptions import AuthenticationError, AuthorizationError, NotFoundError
from app.core.authorization import require_role, UserRoleEnum
from app.db.database import get_session
from app.schemas.user_schema import User
from app.repositories.user_repository import UserRepository

logger = get_logger(__name__)

# Security scheme for JWT tokens
security = HTTPBearer()


def get_db_session() -> Generator[Session, None, None]:
    """Get database session dependency."""
    yield from get_session()


def get_current_user_id(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)]
) -> UUID:
    """Extract and validate user ID from JWT token."""
    try:
        token = credentials.credentials
        user_id_str = verify_token(token)

        if not user_id_str:
            logger.warning("Invalid or expired token")
            raise AuthenticationError("Invalid or expired token")

        user_id = UUID(user_id_str)
        logger.debug("Token validated successfully", user_id=str(user_id))
        return user_id

    except ValueError:
        logger.warning("Invalid token format")
        raise AuthenticationError("Invalid token format")
    except Exception as e:
        logger.error("Token validation error", error=str(e))
        raise AuthenticationError("Token validation failed")


def get_current_user(
    user_id: Annotated[UUID, Depends(get_current_user_id)],
    session: Annotated[Session, Depends(get_db_session)]
) -> User:
    """Get current authenticated user from database."""
    try:
        user_repository = UserRepository()
        user = user_repository.get_by_id(user_id, session)

        if not user:
            logger.warning("User not found", user_id=str(user_id))
            raise NotFoundError("User", str(user_id))

        if not user.is_active:
            logger.warning("User account is inactive", user_id=str(user_id))
            raise AuthorizationError("User account is inactive")

        return user

    except (AuthenticationError, AuthorizationError, NotFoundError):
        raise
    except Exception as e:
        logger.error("Error getting current user", user_id=str(user_id), error=str(e))
        raise AuthenticationError("Error retrieving user information")


def get_current_active_user(
    current_user: Annotated[User, Depends(get_current_user)]
) -> User:
    """Get current active user (alias for clarity)."""
    return current_user


# Role-based access control dependencies
def require_admin(
    current_user: Annotated[User, Depends(get_current_user)]
) -> User:
    """Require admin role for access."""
    if current_user.role != UserRoleEnum.ADMIN:
        logger.warning("Admin access denied", user_id=str(current_user.id), role=current_user.role.value)
        raise AuthorizationError("Admin access required")
    return current_user


def require_admin_or_approver(
    current_user: Annotated[User, Depends(get_current_user)]
) -> User:
    """Require admin or approver role for access."""
    allowed_roles = [UserRoleEnum.ADMIN, UserRoleEnum.APPROVER]
    if current_user.role not in allowed_roles:
        logger.warning("Admin/Approver access denied", user_id=str(current_user.id), role=current_user.role.value)
        raise AuthorizationError("Admin or Approver access required")
    return current_user


def require_employee_or_above(
    current_user: Annotated[User, Depends(get_current_user)]
) -> User:
    """Require employee level or above for access."""
    allowed_roles = [UserRoleEnum.ADMIN, UserRoleEnum.APPROVER, UserRoleEnum.EMPLOYEE]
    if current_user.role not in allowed_roles:
        logger.warning("Employee+ access denied", user_id=str(current_user.id), role=current_user.role.value)
        raise AuthorizationError("Employee level access or above required")
    return current_user


def require_preacher_or_above(
    current_user: Annotated[User, Depends(get_current_user)]
) -> User:
    """Require preacher level or above for access."""
    allowed_roles = [UserRoleEnum.ADMIN, UserRoleEnum.APPROVER, UserRoleEnum.EMPLOYEE, UserRoleEnum.PREACHER]
    if current_user.role not in allowed_roles:
        logger.warning("Preacher+ access denied", user_id=str(current_user.id), role=current_user.role.value)
        raise AuthorizationError("Preacher level access or above required")
    return current_user


# Optional authentication (for public endpoints that can benefit from user context)
def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    session: Annotated[Session, Depends(get_db_session)] = None
) -> Optional[User]:
    """Get current user if authenticated, None otherwise."""
    if not credentials:
        return None

    try:
        user_id_str = verify_token(credentials.credentials)
        if not user_id_str:
            return None

        user_id = UUID(user_id_str)
        user_repository = UserRepository()
        user = user_repository.get_by_id(user_id, session)

        if user and user.is_active:
            return user
        return None

    except Exception:
        return None