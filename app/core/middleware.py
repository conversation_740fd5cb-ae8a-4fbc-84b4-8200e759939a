"""
Custom middleware for request processing and security.

Provides comprehensive logging, error handling, security headers, and performance monitoring.
"""

import time
import uuid
from typing import Op<PERSON>

from fastapi import Request, status
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logging import (
    get_logger,
    log_api_request,
    log_api_response,
    log_error,
    log_security_event,
    request_id_ctx,
    user_id_ctx,
)
from app.core.responses import create_error_response
from app.core.exceptions import BaseAPIException
from app.core.security_constants import (
    SUSPICIOUS_PATTERNS,
    BLOCKED_USER_AGENTS,
    get_rate_limit_for_endpoint,
    INPUT_VALIDATION_CONFIG,
    SECURITY_HEADERS,
)
from app.services.cache_service import cache_service

logger = get_logger(__name__)


class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        try:
            response = await call_next(request)
            return response
        except BaseAPIException as e:
            return self._handle_api_exception(e, request)
        except Exception as e:
            return self._handle_generic_exception(e, request)

    def _handle_api_exception(self, exception: BaseAPIException, request: Request):
        """Handle BaseAPIException with proper logging and response."""
        correlation_id = request_id_ctx.get()
        user_id = user_id_ctx.get()

        # Use the exception's built-in logging context
        request_context = {
            "path": str(request.url.path),
            "method": request.method,
            "user_id": user_id,
            "request_id": correlation_id,
            "client_ip": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
        }

        log_context = exception.get_log_context(request_context)
        logger.warning("API exception occurred", **log_context)

        return create_error_response(
            message=exception.message,
            error_type=exception.error_type or "api_error",
            details=exception.details,
            status_code=exception.status_code,
            request_id=correlation_id,
        )

    def _handle_generic_exception(self, exception: Exception, request: Request):
        """Handle any other exception by converting it to BaseAPIException."""
        from app.core.exceptions import convert_exception

        correlation_id = request_id_ctx.get()
        user_id = user_id_ctx.get()

        # Convert to BaseAPIException for consistent handling
        api_exception = convert_exception(exception, "An unexpected error occurred")

        request_context = {
            "path": str(request.url.path),
            "method": request.method,
            "user_id": user_id,
            "request_id": correlation_id,
            "client_ip": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
            "original_exception": type(exception).__name__,
        }

        log_context = api_exception.get_log_context(request_context)
        logger.error("Unhandled exception converted", **log_context, exc_info=True)

        return create_error_response(
            message=api_exception.message,
            error_type=api_exception.error_type,
            details=api_exception.details,
            status_code=api_exception.status_code,
            request_id=correlation_id,
        )


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Comprehensive request/response logging middleware with correlation IDs."""

    async def dispatch(self, request: Request, call_next):
        # Generate correlation ID for request tracking
        correlation_id = str(uuid.uuid4())
        request_id_ctx.set(correlation_id)

        # Extract user ID from request if available (from JWT token)
        user_id = self._extract_user_id(request)
        if user_id:
            user_id_ctx.set(user_id)

        start_time = time.time()
        request_context = self._get_request_context(request, correlation_id, user_id)
        log_api_request(**request_context)

        try:
            response = await call_next(request)
            response_time = time.time() - start_time

            response.headers["X-Request-ID"] = correlation_id

            log_api_response(
                method=request.method,
                path=str(request.url.path),
                status_code=response.status_code,
                response_time=response_time,
                user_id=user_id,
                request_id=correlation_id,
                response_size=response.headers.get("content-length"),
            )

            from app.utils.performance import record_request_metrics

            record_request_metrics(response_time)

            return response

        except Exception as e:
            response_time = time.time() - start_time

            log_error(
                error=e,
                context={
                    "method": request.method,
                    "path": str(request.url.path),
                    "client_ip": request.client.host if request.client else None,
                    "user_agent": request.headers.get("user-agent"),
                    "response_time": response_time,
                },
                user_id=user_id,
                request_id=correlation_id,
            )

            raise

    def _extract_user_id(self, request: Request) -> Optional[str]:
        """Extract user ID from JWT token in Authorization header."""
        try:
            from app.utils.request_utils import extract_bearer_token

            token = extract_bearer_token(request)

            from app.core.security import verify_token

            user_id = verify_token(token)

            return user_id

        except Exception:
            pass
        return None

    def _get_request_context(
        self, request: Request, correlation_id: str, user_id: Optional[str]
    ) -> dict:
        """Get common request context for logging."""
        return {
            "method": request.method,
            "path": str(request.url.path),
            "user_id": user_id,
            "request_id": correlation_id,
            "client_ip": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
            "query_params": str(request.url.query) if request.url.query else None,
        }

    def _get_error_context(
        self, request: Request, correlation_id: str, user_id: Optional[str]
    ) -> dict:
        """Get common error context for logging."""
        return {
            "path": str(request.url.path),
            "method": request.method,
            "user_id": user_id,
            "request_id": correlation_id,
            "client_ip": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
        }


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Enhanced security headers middleware."""

    async def dispatch(self, request: Request, call_next):
        response = await call_next(request)

        # Add standard security headers from centralized configuration
        for header_name, header_value in SECURITY_HEADERS.items():
            # Skip CSP as it needs special handling for docs
            if header_name != "Content-Security-Policy":
                response.headers[header_name] = header_value

        # More permissive CSP for FastAPI docs while maintaining security
        if request.url.path.startswith("/docs") or request.url.path.startswith(
            "/redoc"
        ):
            # Allow external resources and API connections needed for FastAPI docs
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "connect-src 'self' http://localhost:8000 https://api.surabhi.com; "
                "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; "
                "img-src 'self' data: https://fastapi.tiangolo.com; "
                "font-src 'self' https://cdn.jsdelivr.net; "
                "worker-src 'self' blob:; "
                "child-src 'self' blob:"
            )
        else:
            # Strict CSP for API endpoints
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "connect-src 'self'; "
                "style-src 'self'; "
                "script-src 'self'; "
                "img-src 'self' data:; "
                "font-src 'self'"
            )

        return response


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware using cache service."""

    def __init__(self, app):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        # Skip rate limiting for health checks and docs
        if request.url.path in [
            "/health",
            "/api/v1/health",
            "/docs",
            "/redoc",
            "/openapi.json",
        ]:
            return await call_next(request)

        self.endpoint = request.url.path
        endpoint_type = "default"
        if self.endpoint.startswith("/api/v1/auth"):
            endpoint_type = "auth"
        elif self.endpoint.startswith("/api/v1/"):
            endpoint_type = "api"

        requests, window, burst = get_rate_limit_for_endpoint(endpoint_type).values()

        # Get client identifier
        client_id = self._get_client_id(request)

        # Allow only ${requests} requests per ${window} seconds with a burst limit of ${burst}
        if not await cache_service.token_bucket_rate_limit(
            key=f"token_bucket:{client_id}",
            capacity=burst,
            rate=requests / window,
            requested=1,
        ):
            log_security_event(
                event_type="rate_limit_exceeded",
                description=f"Rate limit exceeded for client {client_id}",
                client_ip=request.client.host if request.client else None,
                path=str(request.url.path),
                method=request.method,
            )
            return create_error_response(
                message="Rate limit exceeded. Please try again later.",
                error_type="rate_limit_exceeded",
                details={"limit": self.requests_per_minute, "window": "1 minute"},
                status_code=429,
            )

        return await call_next(request)

    def _get_client_id(self, request: Request) -> str:
        """Get client identifier for rate limiting."""
        # Try to get user ID from JWT token first
        user_id = user_id_ctx.get()
        if user_id:
            return f"user:{user_id}"

        # Fall back to IP address
        from app.utils.request_utils import get_client_ip

        client_ip = get_client_ip(request)
        return f"ip:{client_ip}"


class RequestValidationMiddleware(BaseHTTPMiddleware):
    """Enhanced request validation and security middleware."""

    def __init__(self, app):
        super().__init__(app)
        self.max_request_size = INPUT_VALIDATION_CONFIG.get(
            "max_request_size", 10 * 1024 * 1024
        )
        self.suspicious_patterns = SUSPICIOUS_PATTERNS
        self.blocked_user_agents = BLOCKED_USER_AGENTS

    async def dispatch(self, request: Request, call_next):
        # Validate request size
        if not await self._validate_request_size(request):
            return create_error_response(
                message="Request payload too large",
                error_type="request_too_large",
                details={"max_size": self.max_request_size},
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            )

        # Validate user agent
        if not await self._validate_user_agent(request):
            return create_error_response(
                message="Blocked user agent",
                error_type="blocked_user_agent",
                status_code=403,
            )

        # Check for suspicious patterns
        if not await self._validate_request_patterns(request):
            return create_error_response(
                message="Invalid request format",
                error_type="invalid_request",
                status_code=400,
            )

        response = await call_next(request)
        return response

    async def _validate_request_size(self, request: Request) -> bool:
        """Validate request size."""
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > self.max_request_size:
            log_security_event(
                event_type="request_too_large",
                description=f"Request size {content_length} exceeds limit {self.max_request_size}",
                client_ip=request.client.host if request.client else None,
                path=str(request.url.path),
                method=request.method,
            )
            return False
        return True

    async def _validate_user_agent(self, request: Request) -> bool:
        """Validate user agent for blocked scanners/bots."""
        user_agent = request.headers.get("user-agent", "").lower()

        for blocked_agent in self.blocked_user_agents:
            if blocked_agent in user_agent:
                log_security_event(
                    event_type="blocked_user_agent",
                    description=f"Blocked user agent: {user_agent}",
                    client_ip=request.client.host if request.client else None,
                    path=str(request.url.path),
                    method=request.method,
                )
                return False
        return True

    async def _validate_request_patterns(self, request: Request) -> bool:
        """Check for suspicious patterns in URL and headers."""
        # Check URL path
        url_path = str(request.url.path).lower()
        query_string = str(request.url.query).lower() if request.url.query else ""

        # Check for suspicious patterns in URL
        for pattern in self.suspicious_patterns:
            if pattern in url_path or pattern in query_string:
                log_security_event(
                    event_type="suspicious_pattern",
                    description=f"Suspicious pattern '{pattern}' detected in URL",
                    client_ip=request.client.host if request.client else None,
                    path=str(request.url.path),
                    method=request.method,
                    pattern=pattern,
                )
                return False

        # Check headers for suspicious content
        for header_name, header_value in request.headers.items():
            if header_value:
                header_value_lower = header_value.lower()
                for pattern in self.suspicious_patterns:
                    if pattern in header_value_lower:
                        log_security_event(
                            event_type="suspicious_header",
                            description=f"Suspicious pattern '{pattern}' detected in header {header_name}",
                            client_ip=request.client.host if request.client else None,
                            path=str(request.url.path),
                            method=request.method,
                            header=header_name,
                            pattern=pattern,
                        )
                        return False

        return True
