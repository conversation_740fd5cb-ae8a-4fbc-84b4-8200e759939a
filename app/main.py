from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from app.core.config import settings
from app.core.logging import configure_logging, get_logger

from app.api.v1 import api_router
from app.core.middleware import (
    RequestLoggingMiddleware,
    ExceptionHandlerMiddleware,
    SecurityHeadersMiddleware,
    RateLimitingMiddleware,
    RequestValidationMiddleware,
)
from app.core.security_constants import API_SECURITY_CONFIG

from app.db.database import close_db_connections, init_database


# Load environment variables from .env file
load_dotenv()

# Initialize structured logging for application observability
configure_logging()
logger = get_logger(__name__)


# Application lifespan handler
@asynccontextmanager
async def lifespan(_: FastAPI):
    logger.info(f"Starting up {settings.APP_NAME} API...")
    init_database()

    logger.info("Application startup completed")
    yield

    logger.info(f"Shutting down {settings.APP_NAME} API...")
    close_db_connections()
    logger.info("Application shutdown completed")


# FastAPI application instance with API documentation
app = FastAPI(
    title=settings.APP_NAME,
    description="A production-ready FastAPI backend application",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    debug=settings.DEBUG,
    lifespan=lifespan,
)

# Add middleware (order matters - they execute in reverse order)
app.add_middleware(ExceptionHandlerMiddleware)
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(RateLimitingMiddleware)
app.add_middleware(RequestValidationMiddleware)
app.add_middleware(
    CORSMiddleware,
    allow_origins=API_SECURITY_CONFIG["cors_origins"],
    allow_credentials=API_SECURITY_CONFIG["allow_credentials"],
    allow_methods=API_SECURITY_CONFIG["cors_methods"],
    allow_headers=API_SECURITY_CONFIG["cors_headers"],
)

# Include API routers
app.include_router(api_router, prefix="/api/v1")


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": f"Welcome to {settings.APP_NAME}",
        "version": settings.API_VERSION,
        "environment": settings.ENVIRONMENT,
        "docs": "/docs",
        "health_endpoint": "/api/v1/health",
        "health_detailed": "/api/v1/health?detailed=true",
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower(),
    )
