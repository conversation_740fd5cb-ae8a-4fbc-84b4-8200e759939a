
.PHONY: help install install-dev install-prod install-test clean test lint format check-deps security-check run dev docker-build docker-run

# Default target
help:
	@echo "Available commands:"
	@echo "  install      - Install production dependencies"
	@echo "  install-dev  - Install all dependencies"
	@echo "  clean        - Clean up cache and temporary files"
	@echo "  test         - Run all tests"
	@echo "  lint         - Run linting checks"
	@echo "  format       - Format code with black and isort"
	@echo "  check-deps   - Check for unused dependencies"
	@echo "  security     - Run security checks"
	@echo "  run          - Run the application"
	@echo "  dev          - Run in development mode"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"

install:
	pip install -r requirements.txt

install-dev:
	pip install -r requirements-dev.txt

clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	rm -rf htmlcov/
	rm -rf dist/
	rm -rf build/

test:
	pytest tests/ -v --cov=app --cov-report=html --cov-report=term

test-unit:
	pytest tests/unit/ -v

test-integration:
	pytest tests/integration/ -v

test-performance:
	pytest tests/performance/ -v

lint:
	flake8 app tests
	mypy app

format:
	black app tests scripts
	isort app tests scripts

format-check:
	black --check app tests scripts
	isort --check-only app tests scripts

security:
	pip-audit

# Development
run:
	uvicorn app.main:app --host 0.0.0.0 --port 8000

dev:
	uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# Docker
docker-build:
	docker build -t surabhi-api .

docker-build-prod:
	docker build --target production -t surabhi-api:prod .

docker-run:
	docker run -p 8000:8000 surabhi-api

docker-compose-up:
	docker-compose up -d

docker-compose-down:
	docker-compose down

# All checks (CI/CD)
check-all: format-check lint test security
