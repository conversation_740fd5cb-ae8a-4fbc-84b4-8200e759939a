# tests/unit/test_exceptions.py
"""
Unit tests for custom exceptions module.
Tests exception hierarchy, error handling decorators, and error mapping.
"""

import pytest
from unittest.mock import Mock, AsyncMock
from uuid import uuid4

from app.core.exceptions import (
    BaseAPIException,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    NotFoundError,
    ConflictError,
    DatabaseError,
    ServiceUnavailableError,
    BusinessLogicError,
    handle_endpoint_errors,
    convert_exception,
    ERROR_MAPPING,
)


class TestBaseAPIException:
    """Test cases for BaseAPIException."""

    def test_base_exception_creation(self):
        """Test creating base API exception."""
        exception = BaseAPIException(
            message="Test error",
            error_type="test_error",
            status_code=400,
            details={"field": "value"},
        )

        assert exception.message == "Test error"
        assert exception.error_type == "test_error"
        assert exception.status_code == 400
        assert exception.details == {"field": "value"}
        assert str(exception) == "Test error"

    def test_base_exception_defaults(self):
        """Test base exception with default values."""
        exception = BaseAPIException("Test error")

        assert exception.message == "Test error"
        assert exception.error_type == "internal_error"
        assert exception.status_code == 500
        assert exception.details == {}


class TestValidationError:
    """Test cases for ValidationError."""

    def test_validation_error_creation(self):
        """Test creating validation error."""
        error = ValidationError(
            message="Invalid input", field_name="email", field_value="invalid-email"
        )

        assert error.message == "Invalid input"
        assert error.error_type == "validation_error"
        assert error.status_code == 400
        assert error.details["field_name"] == "email"
        assert error.details["field_value"] == "invalid-email"

    def test_validation_error_without_field_details(self):
        """Test validation error without field details."""
        error = ValidationError("General validation error")

        assert error.message == "General validation error"
        assert error.error_type == "validation_error"
        assert error.status_code == 400
        assert error.details == {"field": None, "code": None}


class TestAuthenticationError:
    """Test cases for AuthenticationError."""

    def test_authentication_error_creation(self):
        """Test creating authentication error."""
        error = AuthenticationError(
            message="Invalid credentials", auth_method="password"
        )

        assert error.message == "Invalid credentials"
        assert error.error_type == "authentication_error"
        assert error.status_code == 401
        assert error.details["auth_method"] == "password"

    def test_authentication_error_without_method(self):
        """Test authentication error without method."""
        error = AuthenticationError("Authentication failed")

        assert error.message == "Authentication failed"
        assert error.error_type == "authentication_error"
        assert error.status_code == 401
        assert error.details == {}


class TestAuthorizationError:
    """Test cases for AuthorizationError."""

    def test_authorization_error_creation(self):
        """Test creating authorization error."""
        error = AuthorizationError(
            message="Access denied", required_role="admin", user_role="user"
        )

        assert error.message == "Access denied"
        assert error.error_type == "authorization_error"
        assert error.status_code == 403
        assert error.details["required_role"] == "admin"
        assert error.details["user_role"] == "user"

    def test_authorization_error_without_roles(self):
        """Test authorization error without role details."""
        error = AuthorizationError("Insufficient permissions")

        assert error.message == "Insufficient permissions"
        assert error.error_type == "authorization_error"
        assert error.status_code == 403
        assert error.details == {"required_permission": None}


class TestNotFoundError:
    """Test cases for NotFoundError."""

    def test_not_found_error_creation(self):
        """Test creating not found error."""
        resource_id = str(uuid4())
        error = NotFoundError(resource="User", identifier=resource_id)

        assert error.message == f"User not found with identifier: {resource_id}"
        assert error.error_type == "not_found"
        assert error.status_code == 404
        assert error.details["resource"] == "User"
        assert error.details["identifier"] == resource_id

    def test_not_found_error_without_resource_details(self):
        """Test not found error without resource details."""
        error = NotFoundError(resource="Resource")

        assert error.message == "Resource not found"
        assert error.error_type == "not_found"
        assert error.status_code == 404
        assert error.details["resource"] == "Resource"


class TestConflictError:
    """Test cases for ConflictError."""

    def test_conflict_error_creation(self):
        """Test creating conflict error."""
        error = ConflictError(
            message="Email already exists",
            conflict_field="email",
            conflict_value="<EMAIL>",
        )

        assert error.message == "Email already exists"
        assert error.error_type == "conflict_error"
        assert error.status_code == 409
        assert error.details["conflict_field"] == "email"
        assert error.details["conflict_value"] == "<EMAIL>"

    def test_conflict_error_without_conflict_details(self):
        """Test conflict error without conflict details."""
        error = ConflictError("Resource conflict")

        assert error.message == "Resource conflict"
        assert error.error_type == "conflict_error"
        assert error.status_code == 409
        assert error.details == {"resource": None}


class TestDatabaseError:
    """Test cases for DatabaseError."""

    def test_database_error_creation(self):
        """Test creating database error."""
        error = DatabaseError(
            message="Connection failed", operation="user_create", table="users"
        )

        assert error.message == "Connection failed"
        assert error.error_type == "database_error"
        assert error.status_code == 500
        assert error.details["operation"] == "user_create"
        assert error.details["table"] == "users"

    def test_database_error_without_operation_details(self):
        """Test database error without operation details."""
        error = DatabaseError("Database error occurred")

        assert error.message == "Database error occurred"
        assert error.error_type == "database_error"
        assert error.status_code == 500
        assert error.details == {"operation": None}


class TestServiceUnavailableError:
    """Test cases for ServiceUnavailableError."""

    def test_service_unavailable_error_creation(self):
        """Test creating service unavailable error."""
        error = ServiceUnavailableError(
            message="Redis connection failed", service="redis", retry_after=30
        )

        assert error.message == "Redis connection failed"
        assert error.error_type == "service_unavailable"
        assert error.status_code == 503
        assert error.details["service"] == "redis"
        assert error.details["retry_after"] == 30

    def test_service_unavailable_error_without_service_details(self):
        """Test service unavailable error without service details."""
        error = ServiceUnavailableError("Service temporarily unavailable")

        assert error.message == "Service temporarily unavailable"
        assert error.error_type == "service_unavailable"
        assert error.status_code == 503
        assert error.details == {"service": None}


class TestBusinessLogicError:
    """Test cases for BusinessLogicError."""

    def test_business_logic_error_creation(self):
        """Test creating business logic error."""
        error = BusinessLogicError(
            message="Invalid operation",
            business_rule="user_limit_exceeded",
            context={"current_users": 100, "max_users": 50},
        )

        assert error.message == "Invalid operation"
        assert error.error_type == "business_logic_error"
        assert error.status_code == 422
        assert error.details["business_rule"] == "user_limit_exceeded"
        assert error.details["context"]["current_users"] == 100

    def test_business_logic_error_without_rule_details(self):
        """Test business logic error without rule details."""
        error = BusinessLogicError("Business rule violation")

        assert error.message == "Business rule violation"
        assert error.error_type == "business_logic_error"
        assert error.status_code == 422
        assert error.details == {"code": None}


class TestErrorHandlingDecorators:
    """Test cases for error handling decorators."""

    def test_handle_endpoint_errors_success(self):
        """Test handle_endpoint_errors decorator with successful execution."""

        @handle_endpoint_errors
        def test_function():
            return "success"

        result = test_function()
        assert result == "success"

    def test_handle_endpoint_errors_with_api_exception(self):
        """Test handle_endpoint_errors decorator with API exception."""

        @handle_endpoint_errors
        def test_function():
            raise ValidationError("Test validation error")

        with pytest.raises(ValidationError):
            test_function()

    def test_handle_endpoint_errors_with_generic_exception(self):
        """Test handle_endpoint_errors decorator with generic exception."""

        @handle_endpoint_errors
        def test_function():
            raise ValueError("Generic error")

        # Should convert to BaseAPIException
        with pytest.raises(BaseAPIException) as exc_info:
            test_function()

        assert "An unexpected error occurred" in str(exc_info.value)


class TestErrorMapping:
    """Test cases for error mapping functionality."""

    def test_convert_exception_with_mapping(self):
        """Test converting exception using error mapping."""
        # Test ValueError conversion
        original_error = ValueError("Invalid value")
        converted = convert_exception(original_error)

        assert isinstance(converted, ValidationError)
        assert "Invalid value" in converted.message

    def test_convert_exception_without_mapping(self):
        """Test converting exception without mapping."""
        # Test exception not in mapping
        original_error = RuntimeError("Runtime error")
        converted = convert_exception(original_error)

        assert isinstance(converted, BaseAPIException)
        assert "Runtime error" in converted.message

    def test_convert_exception_already_api_exception(self):
        """Test converting exception that's already an API exception."""
        original_error = ValidationError("Already API exception")
        converted = convert_exception(original_error)

        # Should return the same exception
        assert converted is original_error

    def test_error_mapping_completeness(self):
        """Test that error mapping contains expected exception types."""
        expected_mappings = [
            ValueError,
            TypeError,
            KeyError,
            AttributeError,
            ConnectionError,
            TimeoutError,
        ]

        for exception_type in expected_mappings:
            assert exception_type in ERROR_MAPPING
