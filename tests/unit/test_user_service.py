"""
Unit tests for UserService.
Tests the user service business logic with mocked dependencies.
"""

import pytest
from unittest.mock import Mock, patch
from uuid import uuid4
from sqlmodel import Session

from app.services.user_service import UserService
from app.models.user_model import UserCreate, UserResponse
from app.models.auth_model import Token
from app.models.common_models import PaginationParams, PaginatedResponse, PaginationMeta
from app.schemas.user_schema import User, Account, UserRoleEnum
from app.core.exceptions import NotFoundError, BusinessLogicError


class TestUserService:
    """Test cases for UserService."""

    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def user_service(self):
        """Create UserService instance with mocked dependencies."""
        with patch('app.services.user_service.UserRepository') as mock_repo_class, \
             patch('app.services.user_service.UserAuthenticationService') as mock_auth_class:
            
            service = UserService()
            service.user_repository = mock_repo_class.return_value
            service.auth_service = mock_auth_class.return_value
            return service

    @pytest.fixture
    def sample_user_create(self):
        """Sample user creation data."""
        return UserCreate(
            email="<EMAIL>",
            password="SecurePass123!",
            phone_number="+********90",
            first_name="Test",
            last_name="User",
            role=UserRoleEnum.EMPLOYEE
        )

    @pytest.fixture
    def sample_user(self):
        """Sample user entity."""
        return User(
            id=uuid4(),
            email="<EMAIL>",
            phone_number="+********90",
            first_name="Test",
            last_name="User",
            hashed_password="hashed_password",
            role=UserRoleEnum.EMPLOYEE,
            is_active=True,
            is_2fa_enabled=False
        )

    def test_create_user_success(self, user_service, mock_session, sample_user_create, sample_user):
        """Test successful user creation."""
        # Setup mocks
        user_service.user_repository.get_by_email.return_value = None
        user_service.auth_service.hash_password.return_value = "hashed_password"
        user_service.user_repository.create.return_value = sample_user

        # Execute
        result = user_service.create_user(sample_user_create, mock_session)

        # Verify
        assert isinstance(result, UserResponse)
        assert result.email == sample_user_create.email
        user_service.user_repository.get_by_email.assert_called_once_with(sample_user_create.email, mock_session)
        user_service.auth_service.hash_password.assert_called_once_with(sample_user_create.password)
        user_service.user_repository.create.assert_called_once()

    def test_create_user_already_exists(self, user_service, mock_session, sample_user_create, sample_user):
        """Test user creation when user already exists."""
        # Setup mocks
        user_service.user_repository.get_by_email.return_value = sample_user

        # Execute and verify
        with pytest.raises(BusinessLogicError, match="already exists"):
            user_service.create_user(sample_user_create, mock_session)

    def test_get_user_by_id_success(self, user_service, mock_session, sample_user):
        """Test successful user retrieval by ID."""
        # Setup mocks
        user_service.user_repository.get_by_id.return_value = sample_user

        # Execute
        result = user_service.get_user_by_id(sample_user.id, mock_session)

        # Verify
        assert isinstance(result, UserResponse)
        assert result.id == sample_user.id
        user_service.user_repository.get_by_id.assert_called_once_with(sample_user.id, mock_session)

    def test_get_user_by_id_not_found(self, user_service, mock_session):
        """Test user retrieval when user not found."""
        # Setup mocks
        user_id = uuid4()
        user_service.user_repository.get_by_id.return_value = None

        # Execute and verify
        with pytest.raises(NotFoundError, match="not found"):
            user_service.get_user_by_id(user_id, mock_session)

    def test_get_user_by_email_success(self, user_service, mock_session, sample_user):
        """Test successful user retrieval by email."""
        # Setup mocks
        user_service.user_repository.get_by_email.return_value = sample_user

        # Execute
        result = user_service.get_user_by_email(sample_user.email, mock_session)

        # Verify
        assert isinstance(result, UserResponse)
        assert result.email == sample_user.email
        user_service.user_repository.get_by_email.assert_called_once_with(sample_user.email, mock_session)

    def test_list_users_success(self, user_service, mock_session, sample_user):
        """Test successful user listing."""
        # Setup mocks
        pagination = PaginationParams(page=1, size=10)
        meta = PaginationMeta(page=1, size=10, total=1, pages=1, has_next=False, has_prev=False)
        paginated_response = PaginatedResponse(items=[sample_user], meta=meta)
        user_service.user_repository.get_paginated.return_value = paginated_response

        # Execute
        result = user_service.list_users(mock_session, pagination)

        # Verify
        assert isinstance(result, PaginatedResponse)
        assert len(result.items) == 1
        assert isinstance(result.items[0], UserResponse)
        user_service.user_repository.get_paginated.assert_called_once()

    def test_deactivate_user_success(self, user_service, mock_session, sample_user):
        """Test successful user deactivation."""
        # Setup mocks
        user_service.user_repository.get_by_id.return_value = sample_user
        deactivated_user = User(**sample_user.model_dump())
        deactivated_user.is_active = False
        user_service.user_repository.update.return_value = deactivated_user

        # Execute
        result = user_service.deactivate_user(sample_user.id, mock_session)

        # Verify
        assert isinstance(result, UserResponse)
        assert not result.is_active
        user_service.user_repository.get_by_id.assert_called_once_with(sample_user.id, mock_session)
        user_service.user_repository.update.assert_called_once()

    def test_authenticate_user_delegates_to_auth_service(self, user_service, mock_session, sample_user):
        """Test that authenticate_user delegates to auth service."""
        # Setup mocks
        user_service.auth_service.authenticate_user.return_value = sample_user

        # Execute
        result = user_service.authenticate_user("<EMAIL>", "password", mock_session)

        # Verify
        assert result == sample_user
        user_service.auth_service.authenticate_user.assert_called_once_with("<EMAIL>", "password", mock_session)

    def test_create_user_tokens_delegates_to_auth_service(self, user_service, sample_user):
        """Test that create_user_tokens delegates to auth service."""
        # Setup mocks
        token = Token(access_token="access", token_type="bearer", refresh_token="refresh")
        user_service.auth_service.create_user_tokens.return_value = token

        # Execute
        result = user_service.create_user_tokens(sample_user)

        # Verify
        assert result == token
        user_service.auth_service.create_user_tokens.assert_called_once_with(sample_user)
