# tests/unit/test_utils.py
"""
Unit tests for organized utils modules.
Tests utility functions in isolation with various input scenarios.
"""

import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock
from fastapi import Request

from app.utils.formatters import (
    format_currency,
    format_phone_number,
    mask_email,
    mask_phone_number,
    normalize_email,
    format_file_size,
    format_duration,
)
from app.utils.datetime_utils import (
    calculate_age,
    is_expired,
    time_until_expiry,
    format_datetime,
    parse_datetime,
)
from app.utils.validators import (
    sanitize_input,
    validate_json_schema,
    sanitize_search_query,
    sanitize_html,
)
from app.utils.string_utils import (
    is_valid_url,
    extract_domain,
    remove_extra_whitespace,
    slugify,
    generate_initials,
    mask_sensitive_data,
    count_words,
    estimate_reading_time,
    highlight_search_terms,
    snake_to_camel_case,
    camel_to_snake_case,
)


class TestFormatters:
    """Test cases for formatters module."""

    def test_format_currency_inr(self):
        """Test formatting currency in INR."""
        assert format_currency(1234.56) == "₹1.2K"
        assert format_currency(0) == "₹0.00"
        assert format_currency(100000) == "₹1.0L"
        assert format_currency(10000000) == "₹1.0Cr"

    def test_format_currency_other_currencies(self):
        """Test formatting currency in other currencies."""
        assert format_currency(1234.56, "USD") == "USD 1,234.56"
        assert format_currency(1234.56, "EUR") == "EUR 1,234.56"

    def test_format_phone_number(self):
        """Test formatting phone numbers."""
        assert format_phone_number("9876543210") == "+919876543210"
        assert format_phone_number("919876543210") == "+919876543210"
        assert format_phone_number("invalid") == "invalid"

    def test_mask_email(self):
        """Test masking email addresses."""
        assert mask_email("<EMAIL>") == "t**<EMAIL>"
        assert mask_email("<EMAIL>") == "<EMAIL>"

    def test_mask_phone_number(self):
        """Test masking phone numbers."""
        assert mask_phone_number("+919876543210") == "+9*********10"
        assert mask_phone_number("1234") == "1234"  # Too short to mask

    def test_normalize_email(self):
        """Test normalizing email addresses."""
        assert normalize_email("<EMAIL>") == "<EMAIL>"
        assert normalize_email("<EMAIL>") == "<EMAIL>"
        assert normalize_email("<EMAIL>") == "<EMAIL>"

    def test_format_file_size(self):
        """Test formatting file sizes."""
        assert format_file_size(1024) == "1.0 KB"
        assert format_file_size(1048576) == "1.0 MB"
        assert format_file_size(1073741824) == "1.0 GB"
        assert format_file_size(500) == "500.0 B"
        assert format_file_size(0) == "0 B"

    def test_format_duration(self):
        """Test formatting durations."""
        assert format_duration(3661) == "1h 1m"
        assert format_duration(61) == "1m 1s"
        assert format_duration(30) == "30s"
        assert format_duration(60) == "1m"
        assert format_duration(3600) == "1h"


class TestDatetimeUtils:
    """Test cases for datetime_utils module."""

    def test_calculate_age(self):
        """Test calculating age from birth date."""
        # Person born 25 years ago
        birth_date = datetime.now(timezone.utc) - timedelta(days=365 * 25)
        age = calculate_age(birth_date)
        assert age == 24 or age == 25  # Account for leap years

    def test_is_expired(self):
        """Test checking if datetime has expired."""
        past_time = datetime.now(timezone.utc) - timedelta(hours=1)
        future_time = datetime.now(timezone.utc) + timedelta(hours=1)

        assert is_expired(past_time)
        assert not is_expired(future_time)

    def test_time_until_expiry(self):
        """Test calculating time until expiry."""
        future_time = datetime.now(timezone.utc) + timedelta(hours=2)
        time_until = time_until_expiry(future_time)

        assert isinstance(time_until, timedelta)
        assert time_until.total_seconds() > 7000  # Approximately 2 hours

    def test_format_datetime(self):
        """Test formatting datetime objects."""
        dt = datetime(2023, 12, 25, 15, 30, 45, tzinfo=timezone.utc)
        formatted = format_datetime(dt)
        assert formatted == "2023-12-25 15:30:45 UTC"

        # Test custom format
        custom_format = format_datetime(dt, "%Y-%m-%d")
        assert custom_format == "2023-12-25"

    def test_format_datetime_naive(self):
        """Test formatting naive datetime (adds UTC timezone)."""
        dt = datetime(2023, 12, 25, 15, 30, 45)  # No timezone
        formatted = format_datetime(dt)
        assert formatted == "2023-12-25 15:30:45 UTC"

    def test_parse_datetime_iso(self):
        """Test parsing ISO datetime string."""
        iso_string = "2023-12-25T15:30:45+00:00"
        dt = parse_datetime(iso_string)
        assert dt.year == 2023
        assert dt.month == 12
        assert dt.day == 25
        assert dt.hour == 15
        assert dt.minute == 30
        assert dt.second == 45

    def test_parse_datetime_common_formats(self):
        """Test parsing common datetime formats."""
        # Test various formats
        dt1 = parse_datetime("2023-12-25 15:30:45")
        assert dt1.year == 2023

        dt2 = parse_datetime("2023-12-25")
        assert dt2.year == 2023
        assert dt2.month == 12
        assert dt2.day == 25

    def test_parse_datetime_invalid(self):
        """Test parsing invalid datetime string."""
        with pytest.raises(ValueError):
            parse_datetime("invalid-datetime")


class TestValidators:
    """Test cases for validators module."""

    def test_sanitize_input(self):
        """Test input sanitization."""
        assert sanitize_input("  Hello World  ") == "Hello World"
        assert sanitize_input("Normal text") == "Normal text"
        # Test with max length
        long_text = "a" * 100
        assert len(sanitize_input(long_text, max_length=50)) == 50

    def test_validate_json_schema(self):
        """Test JSON schema validation."""
        data = {"name": "John", "email": "<EMAIL>"}
        required_fields = ["name", "email"]
        is_valid, missing = validate_json_schema(data, required_fields)
        assert is_valid
        assert missing == []

        # Test missing fields
        incomplete_data = {"name": "John"}
        is_valid, missing = validate_json_schema(incomplete_data, required_fields)
        assert not is_valid
        assert "email" in missing

    def test_sanitize_search_query(self):
        """Test search query sanitization."""
        assert sanitize_search_query("normal search") == "normal search"
        assert sanitize_search_query("search*with+regex") == "searchwithregex"
        # Test length limit
        long_query = "a" * 200
        assert len(sanitize_search_query(long_query)) <= 100

    def test_sanitize_html(self):
        """Test HTML sanitization."""
        assert sanitize_html("<script>alert('xss')</script>") == "alert('xss')"
        assert sanitize_html("<p>Hello <b>World</b></p>") == "Hello World"
        assert sanitize_html("Normal text") == "Normal text"


class TestStringUtils:
    """Test cases for string_utils module."""

    def test_is_valid_url(self):
        """Test URL validation."""
        assert is_valid_url("https://example.com")
        assert is_valid_url("http://subdomain.example.com/path")
        assert is_valid_url("https://localhost:8000")
        assert not is_valid_url("invalid-url")
        assert not is_valid_url("")

    def test_extract_domain(self):
        """Test extracting domain from URL."""
        assert extract_domain("https://example.com/path") == "example.com"
        assert extract_domain("example.com") == "example.com"
        assert extract_domain("subdomain.example.com") == "subdomain.example.com"
        assert extract_domain("") is None

    def test_clean_whitespace(self):
        """Test cleaning excessive whitespace."""
        assert remove_extra_whitespace("  multiple   spaces  ") == "multiple spaces"
        assert remove_extra_whitespace("tabs\tand\nnewlines") == "tabs and newlines"
        assert remove_extra_whitespace("normal text") == "normal text"
        assert remove_extra_whitespace("") == ""

    def test_slugify(self):
        """Test converting text to URL-friendly slug."""
        assert slugify("Hello World!") == "hello-world"
        assert slugify("<EMAIL>") == "testemailcom"
        assert slugify("Multiple   Spaces") == "multiple-spaces"
        assert slugify("") == ""

    def test_slugify_max_length(self):
        """Test slugify with max length."""
        long_text = "This is a very long text that should be truncated"
        slug = slugify(long_text, max_length=20)
        assert len(slug) <= 20
        assert not slug.endswith("-")

    def test_generate_initials(self):
        """Test generating initials from name."""
        assert generate_initials("John Doe") == "JD"
        assert generate_initials("John Michael Doe") == "JM"
        assert generate_initials("John Michael Doe", max_chars=3) == "JMD"
        assert generate_initials("") == ""

    def test_mask_sensitive_data(self):
        """Test masking sensitive data."""
        assert mask_sensitive_data("1234567890",'*',2) == "12******90"
        assert mask_sensitive_data("short") == "sh*rt"
        assert mask_sensitive_data("") == ""

    def test_count_words(self):
        """Test counting words."""
        assert count_words("Hello world") == 2
        assert count_words("One two three four five") == 5
        assert count_words("") == 0
        assert count_words("   multiple   spaces   ") == 2

    def test_estimate_reading_time(self):
        """Test estimating reading time."""
        text = " ".join(["word"] * 200)  # 200 words
        assert estimate_reading_time(text) == 1  # 1 minute at 200 wpm

        text = " ".join(["word"] * 400)  # 400 words
        assert estimate_reading_time(text) == 2  # 2 minutes

    def test_highlight_search_terms(self):
        """Test highlighting search terms."""
        text = "This is a test text"
        terms = ["test", "text"]
        highlighted = highlight_search_terms(text, terms)
        assert "<mark>test</mark>" in highlighted
        assert "<mark>text</mark>" in highlighted

    def test_snake_to_camel_case(self):
        """Test converting snake_case to camelCase."""
        assert snake_to_camel_case("snake_case") == "snakeCase"
        assert snake_to_camel_case("test_function_name") == "testFunctionName"
        assert snake_to_camel_case("simple") == "simple"

    def test_camel_to_snake_case(self):
        """Test converting camelCase to snake_case."""
        assert camel_to_snake_case("camelCase") == "camel_case"
        assert camel_to_snake_case("testFunctionName") == "test_function_name"
        assert camel_to_snake_case("simple") == "simple"


class TestRequestUtils:
    """Test cases for request_utils module."""

    @pytest.fixture
    def mock_request(self):
        """Create mock FastAPI request."""
        request = Mock(spec=Request)
        request.headers = {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "accept-language": "en-US,en;q=0.9",
            "accept-encoding": "gzip, deflate, br",
            "host": "api.example.com",
            "origin": "https://example.com",
            "referer": "https://example.com/page",
        }
        request.client = Mock()
        request.client.host = "*************"
        request.url = Mock()
        request.url.path = "/api/v1/users"
        request.url.query = "page=1&limit=10"
        request.method = "GET"
        request.query_params = {"page": "1", "limit": "10"}
        return request

    def test_extract_client_info(self, mock_request):
        """Test extracting client information from request."""
        from app.utils.request_utils import extract_client_info

        client_info = extract_client_info(mock_request)

        assert client_info["ip_address"] == "*************"
        assert "Mozilla" in client_info["user_agent"]
        assert client_info["host"] == "api.example.com"
        assert client_info["origin"] == "https://example.com"

    def test_get_client_ip_direct(self, mock_request):
        """Test getting client IP from direct connection."""
        from app.utils.request_utils import get_client_ip

        ip = get_client_ip(mock_request)
        assert ip == "*************"

    def test_get_client_ip_forwarded(self, mock_request):
        """Test getting client IP from forwarded headers."""
        from app.utils.request_utils import get_client_ip

        mock_request.headers["x-forwarded-for"] = "***********, *************"
        ip = get_client_ip(mock_request)
        assert ip == "***********"

    def test_get_client_ip_real_ip(self, mock_request):
        """Test getting client IP from real IP header."""
        from app.utils.request_utils import get_client_ip

        mock_request.headers["x-real-ip"] = "***********"
        ip = get_client_ip(mock_request)
        assert ip == "***********"

    def test_extract_bearer_token(self, mock_request):
        """Test extracting bearer token from authorization header."""
        from app.utils.request_utils import extract_bearer_token

        mock_request.headers["authorization"] = (
            "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
        )
        token = extract_bearer_token(mock_request)
        assert token == "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"

    def test_extract_bearer_token_no_header(self, mock_request):
        """Test extracting bearer token when no authorization header."""
        from app.utils.request_utils import extract_bearer_token

        token = extract_bearer_token(mock_request)
        assert token is None

    def test_extract_bearer_token_invalid_format(self, mock_request):
        """Test extracting bearer token with invalid format."""
        from app.utils.request_utils import extract_bearer_token

        mock_request.headers["authorization"] = "Basic dXNlcjpwYXNz"
        token = extract_bearer_token(mock_request)
        assert token is None

    def test_is_mobile_request(self, mock_request):
        """Test detecting mobile requests."""
        from app.utils.request_utils import is_mobile_request

        # Desktop user agent
        assert not is_mobile_request(mock_request)

        # Mobile user agent
        mock_request.headers["user-agent"] = (
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)"
        )
        assert is_mobile_request(mock_request)

    def test_is_bot_request(self, mock_request):
        """Test detecting bot requests."""
        from app.utils.request_utils import is_bot_request

        # Normal user agent
        assert not is_bot_request(mock_request)

        # Bot user agent
        mock_request.headers["user-agent"] = (
            "Googlebot/2.1 (+http://www.google.com/bot.html)"
        )
        assert is_bot_request(mock_request)

    def test_get_request_context(self, mock_request):
        """Test getting comprehensive request context."""
        from app.utils.request_utils import get_request_context

        context = get_request_context(mock_request)

        assert context["method"] == "GET"
        assert context["path"] == "/api/v1/users"
        assert context["client_ip"] == "*************"
        assert "user_agent" in context
        assert "is_mobile" in context
        assert "is_bot" in context
        assert context["query_params"] == {"page": "1", "limit": "10"}


class TestPerformanceUtils:
    """Test cases for performance utils module."""

    def test_get_system_metrics(self):
        """Test getting system metrics."""
        from app.utils.performance import get_system_metrics

        metrics = get_system_metrics()

        assert "cpu" in metrics
        assert "memory" in metrics
        assert "disk" in metrics
        assert "usage_percent" in metrics["cpu"]
        assert "usage_percent" in metrics["memory"]
        assert "usage_percent" in metrics["disk"]
        assert isinstance(metrics["cpu"]["usage_percent"], (int, float))
        assert isinstance(metrics["memory"]["usage_percent"], (int, float))
        assert isinstance(metrics["disk"]["usage_percent"], (int, float))

    def test_get_process_metrics(self):
        """Test getting process metrics."""
        from app.utils.performance import get_process_metrics

        metrics = get_process_metrics()

        assert "cpu_percent" in metrics
        assert "memory" in metrics
        assert "uptime_seconds" in metrics
        assert "status" in metrics
        assert isinstance(metrics["uptime_seconds"], (int, float))

    def test_get_application_metrics(self):
        """Test getting application metrics."""
        from app.utils.performance import get_application_metrics

        metrics = get_application_metrics()

        assert "uptime_seconds" in metrics
        assert "total_requests" in metrics
        assert "average_response_time_ms" in metrics
        assert "status" in metrics
        assert isinstance(metrics["total_requests"], int)

    def test_record_request_metrics(self):
        """Test recording request metrics."""
        from app.utils.performance import (
            record_request_metrics,
            get_application_metrics,
        )

        # Record some metrics
        record_request_metrics(0.1)  # 100ms
        record_request_metrics(0.2)  # 200ms

        metrics = get_application_metrics()
        assert metrics["total_requests"] >= 2
        assert metrics["average_response_time_ms"] > 0

    def test_format_uptime(self):
        """Test formatting uptime."""
        from app.utils.performance import format_uptime

        assert format_uptime(30) == "30s"
        assert format_uptime(90) == "1m 30s"
        assert format_uptime(3661) == "1h 1m 1s"
        assert format_uptime(90061) == "1d 1h 1m 1s"

    def test_get_comprehensive_metrics(self):
        """Test getting comprehensive metrics."""
        from app.utils.performance import get_comprehensive_metrics

        metrics = get_comprehensive_metrics()

        assert "system" in metrics
        assert "process" in metrics
        assert "application" in metrics
        assert "timestamp" in metrics
