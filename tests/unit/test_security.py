# tests/unit/test_security.py
"""
Unit tests for security module.
Tests password hashing, JWT tokens, and security utilities.
"""

from datetime import datetime, timezone, timedelta
from jose import jwt

from app.core.security import (
    get_password_hash,
    verify_password,
    create_access_token,
    create_refresh_token,
    verify_token,
    generate_otp,
    hash_string,
    verify_hash,
    generate_random_string,
)
from app.core.config import settings


class TestPasswordHashing:
    """Test cases for password hashing functions."""

    def test_get_password_hash(self):
        """Test password hashing."""
        password = "TestPassword123!"
        hashed = get_password_hash(password)

        assert hashed != password
        assert len(hashed) > 50  # Bcrypt hashes are typically 60 characters
        assert hashed.startswith("$2b$")

    def test_verify_password_correct(self):
        """Test password verification with correct password."""
        password = "TestPassword123!"
        hashed = get_password_hash(password)

        assert verify_password(password, hashed)

    def test_verify_password_incorrect(self):
        """Test password verification with incorrect password."""
        password = "TestPassword123!"
        wrong_password = "WrongPassword123!"
        hashed = get_password_hash(password)

        assert not verify_password(wrong_password, hashed)

    def test_password_hash_uniqueness(self):
        """Test that same password produces different hashes."""
        password = "TestPassword123!"
        hash1 = get_password_hash(password)
        hash2 = get_password_hash(password)

        assert hash1 != hash2
        assert verify_password(password, hash1)
        assert verify_password(password, hash2)


class TestJWTTokens:
    """Test cases for JWT token functions."""

    def test_create_access_token(self):
        """Test creating access token."""
        subject = "<EMAIL>"
        token = create_access_token(subject)

        assert isinstance(token, str)
        assert len(token) > 100  # JWT tokens are typically long

        # Decode and verify
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        assert payload["sub"] == "<EMAIL>"
        assert "exp" in payload

    def test_create_access_token_with_custom_expiry(self):
        """Test creating access token with custom expiry."""
        subject = "<EMAIL>"
        expires_delta = timedelta(minutes=15)
        token = create_access_token(subject, expires_delta)

        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        exp_time = datetime.fromtimestamp(payload["exp"], tz=timezone.utc)
        now = datetime.now(timezone.utc)

        # Should expire in approximately 15 minutes
        time_diff = exp_time - now
        assert 14 * 60 < time_diff.total_seconds() < 16 * 60

    def test_create_refresh_token(self):
        """Test creating refresh token."""
        subject = "<EMAIL>"
        token = create_refresh_token(subject)

        assert isinstance(token, str)
        assert len(token) > 100

        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        assert payload["sub"] == "<EMAIL>"
        assert payload["type"] == "refresh"

    def test_verify_token_valid(self):
        """Test verifying valid token."""
        subject = "<EMAIL>"
        token = create_access_token(subject)

        result = verify_token(token)
        assert result == "<EMAIL>"

    def test_verify_token_invalid(self):
        """Test verifying invalid token."""
        invalid_token = "invalid.jwt.token"

        result = verify_token(invalid_token)
        assert result is None

    def test_verify_token_expired(self):
        """Test verifying expired token."""
        subject = "<EMAIL>"
        # Create token that expires immediately
        expires_delta = timedelta(seconds=-1)
        token = create_access_token(subject, expires_delta)

        result = verify_token(token)
        assert result is None


class TestOTPFunctions:
    """Test cases for OTP functions."""

    def test_generate_otp(self):
        """Test OTP generation."""
        otp = generate_otp()

        assert isinstance(otp, str)
        assert len(otp) == 6
        assert otp.isdigit()

    def test_generate_otp_custom_length(self):
        """Test OTP generation with custom length."""
        otp = generate_otp(length=8)

        assert len(otp) == 8
        assert otp.isdigit()


class TestSensitiveDataHashing:
    """Test cases for sensitive data hashing functions."""

    def test_hash_string(self):
        """Test hashing string data."""
        data = "sensitive-information"
        hashed = hash_string(data)

        assert hashed != data
        assert ":" in hashed  # Format is salt:hash
        assert isinstance(hashed, str)

    def test_verify_hash_correct(self):
        """Test verifying hash with correct data."""
        data = "sensitive-information"
        hashed = hash_string(data)

        assert verify_hash(data, hashed)

    def test_verify_hash_incorrect(self):
        """Test verifying hash with incorrect data."""
        data = "sensitive-information"
        wrong_data = "wrong-information"
        hashed = hash_string(data)

        assert not verify_hash(wrong_data, hashed)

    def test_hash_string_with_salt(self):
        """Test hashing string with custom salt."""
        data = "sensitive-information"
        salt = "custom-salt"
        hashed = hash_string(data, salt)

        assert hashed != data
        assert hashed.startswith(f"{salt}:")
        assert verify_hash(data, hashed)


class TestSecurityUtilities:
    """Test cases for security utility functions."""

    def test_generate_random_string(self):
        """Test generating random string."""
        random_str = generate_random_string(32)

        assert len(random_str) == 32
        assert isinstance(random_str, str)

        # Generate another and ensure they're different
        random_str2 = generate_random_string(32)
        assert random_str != random_str2

    def test_generate_random_string_different_lengths(self):
        """Test generating random strings of different lengths."""
        for length in [16, 32, 64, 128]:
            random_str = generate_random_string(length)
            assert len(random_str) == length
