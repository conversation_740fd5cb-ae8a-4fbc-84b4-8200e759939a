"""
Unit tests for AuthService.
Tests authentication and token management functionality.
"""

import pytest
from unittest.mock import Mock, patch
from uuid import uuid4
from sqlmodel import Session

from app.services.auth_service import UserAuthenticationService
from app.models.auth_model import Token
from app.schemas.user_schema import User, UserRoleEnum
from app.core.exceptions import AuthenticationError


class TestUserAuthenticationService:
    """Test cases for UserAuthenticationService."""

    @pytest.fixture
    def mock_session(self):
        """Mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def auth_service(self):
        """Create AuthService instance with mocked dependencies."""
        with patch('app.services.auth_service.UserRepository') as mock_repo_class:
            service = UserAuthenticationService()
            service.user_repository = mock_repo_class.return_value
            return service

    @pytest.fixture
    def sample_user(self):
        """Sample user entity."""
        return User(
            id=uuid4(),
            email="<EMAIL>",
            phone_number="+1234567890",
            first_name="Test",
            last_name="User",
            hashed_password="$2b$12$hashed_password",
            role=UserRoleEnum.EMPLOYEE,
            is_active=True,
            is_2fa_enabled=False
        )

    def test_hash_password(self, auth_service):
        """Test password hashing."""
        password = "SecurePass123!"
        hashed = auth_service.hash_password(password)
        
        assert hashed != password
        assert hashed.startswith("$2b$")
        assert len(hashed) > 50

    def test_verify_password_correct(self, auth_service):
        """Test password verification with correct password."""
        password = "SecurePass123!"
        hashed = auth_service.hash_password(password)
        
        assert auth_service.verify_password(password, hashed) is True

    def test_verify_password_incorrect(self, auth_service):
        """Test password verification with incorrect password."""
        password = "SecurePass123!"
        wrong_password = "WrongPassword"
        hashed = auth_service.hash_password(password)
        
        assert auth_service.verify_password(wrong_password, hashed) is False

    def test_authenticate_user_success(self, auth_service, mock_session, sample_user):
        """Test successful user authentication."""
        # Setup mocks
        password = "SecurePass123!"
        sample_user.hashed_password = auth_service.hash_password(password)
        auth_service.user_repository.get_by_email.return_value = sample_user

        # Execute
        result = auth_service.authenticate_user(sample_user.email, password, mock_session)

        # Verify
        assert result == sample_user
        auth_service.user_repository.get_by_email.assert_called_once_with(sample_user.email, mock_session)

    def test_authenticate_user_not_found(self, auth_service, mock_session):
        """Test authentication when user not found."""
        # Setup mocks
        auth_service.user_repository.get_by_email.return_value = None

        # Execute
        result = auth_service.authenticate_user("<EMAIL>", "password", mock_session)

        # Verify
        assert result is None

    def test_authenticate_user_wrong_password(self, auth_service, mock_session, sample_user):
        """Test authentication with wrong password."""
        # Setup mocks
        password = "SecurePass123!"
        wrong_password = "WrongPassword"
        sample_user.hashed_password = auth_service.hash_password(password)
        auth_service.user_repository.get_by_email.return_value = sample_user

        # Execute
        result = auth_service.authenticate_user(sample_user.email, wrong_password, mock_session)

        # Verify
        assert result is None

    def test_authenticate_user_inactive(self, auth_service, mock_session, sample_user):
        """Test authentication with inactive user."""
        # Setup mocks
        password = "SecurePass123!"
        sample_user.hashed_password = auth_service.hash_password(password)
        sample_user.is_active = False
        auth_service.user_repository.get_by_email.return_value = sample_user

        # Execute
        result = auth_service.authenticate_user(sample_user.email, password, mock_session)

        # Verify
        assert result is None

    @patch('app.services.auth_service.create_access_token')
    @patch('app.services.auth_service.create_refresh_token')
    def test_create_user_tokens(self, mock_refresh_token, mock_access_token, auth_service, sample_user):
        """Test token creation for user."""
        # Setup mocks
        mock_access_token.return_value = "access_token_123"
        mock_refresh_token.return_value = "refresh_token_456"

        # Execute
        result = auth_service.create_user_tokens(sample_user)

        # Verify
        assert isinstance(result, Token)
        assert result.access_token == "access_token_123"
        assert result.refresh_token == "refresh_token_456"
        assert result.token_type == "bearer"
        
        mock_access_token.assert_called_once()
        mock_refresh_token.assert_called_once()

    def test_create_user_tokens_data_structure(self, auth_service, sample_user):
        """Test that create_user_tokens creates proper data structure."""
        with patch('app.services.auth_service.create_access_token') as mock_access, \
             patch('app.services.auth_service.create_refresh_token') as mock_refresh:
            
            mock_access.return_value = "access_123"
            mock_refresh.return_value = "refresh_456"
            
            result = auth_service.create_user_tokens(sample_user)
            
            # Verify token data includes user information
            call_args = mock_access.call_args[0][0]  # First argument to create_access_token
            assert call_args["sub"] == sample_user.email
            assert call_args["user_id"] == str(sample_user.id)
            assert call_args["role"] == sample_user.role.value
