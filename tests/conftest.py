"""
Pytest configuration and fixtures for testing.
"""

import pytest
import asyncio
from typing import Generator
from fastapi.testclient import Test<PERSON>lient
from sqlmodel import Session, create_engine, SQLModel
from sqlmodel.pool import StaticPool
from unittest.mock import Mock

from app.main import app
from app.db.database import get_session
from app.core.config import settings
from app.schemas.user_schema import User, Account


# Test database URL (in-memory SQLite for testing)
TEST_DATABASE_URL = "sqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="function")
def test_engine():
    """Create a test database engine."""
    engine = create_engine(
        TEST_DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
    SQLModel.metadata.create_all(engine)
    yield engine
    SQLModel.metadata.drop_all(engine)


@pytest.fixture(scope="function")
def test_session(test_engine) -> Generator[Session, None, None]:
    """Create a test database session."""
    with Session(test_engine) as session:
        yield session


@pytest.fixture(scope="function")
def client(test_session: Session) -> Generator[TestClient, None, None]:
    """Create a test client with dependency overrides."""

    def get_test_session():
        return test_session

    app.dependency_overrides[get_session] = get_test_session

    with TestClient(app) as test_client:
        yield test_client

    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def mock_redis():
    """Mock Redis client for testing."""
    mock_redis = Mock()
    mock_redis.get.return_value = None
    mock_redis.set.return_value = True
    mock_redis.delete.return_value = True
    mock_redis.exists.return_value = False
    mock_redis.expire.return_value = True
    mock_redis.ttl.return_value = -1
    mock_redis.hset.return_value = True
    mock_redis.hget.return_value = None
    mock_redis.hgetall.return_value = {}
    mock_redis.hdel.return_value = True
    mock_redis.incr.return_value = 1
    mock_redis.setex.return_value = True
    mock_redis.ping.return_value = True
    return mock_redis


@pytest.fixture(scope="function")
def test_user_data():
    """Sample user data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "phone_number": "+919876543210",
        "first_name": "Test",
        "last_name": "User",
        "role": "user",
    }


@pytest.fixture(scope="function")
def test_admin_data():
    """Sample admin user data for testing."""
    return {
        "email": "<EMAIL>",
        "password": "AdminPassword123!",
        "phone_number": "+919876543211",
        "first_name": "Admin",
        "last_name": "User",
        "role": "admin",
    }


@pytest.fixture(scope="function")
def test_user(test_session: Session, test_user_data: dict) -> User:
    """Create a test user in the database."""
    from app.core.security import get_password_hash
    from datetime import datetime, timezone

    user = User(
        email=test_user_data["email"],
        phone_number=test_user_data["phone_number"],
        first_name=test_user_data["first_name"],
        last_name=test_user_data["last_name"],
        hashed_password=get_password_hash(test_user_data["password"]),
        role=test_user_data["role"],
        is_active=True,
        is_2fa_enabled=False,
    )

    test_session.add(user)
    test_session.commit()
    test_session.refresh(user)

    # Create associated account with tokens
    from app.core.security import create_access_token, create_refresh_token
    from app.core.config import settings
    from datetime import timedelta

    access_token = create_access_token(data={"sub": user.email})
    refresh_token = create_refresh_token(data={"sub": user.email})

    account = Account(
        user_id=user.id,
        access_token=access_token,
        expires_at=datetime.now(timezone.utc) + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES),
        refresh_token=refresh_token,
        refresh_token_expires_at=datetime.now(timezone.utc) + timedelta(minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES),
        last_login=datetime.now(timezone.utc),
        last_failed_login_attempts=0,
    )

    test_session.add(account)
    test_session.commit()
    test_session.refresh(account)
    test_session.refresh(user)

    return user


@pytest.fixture(scope="function")
def test_admin_user(test_session: Session, test_admin_data: dict) -> User:
    """Create a test admin user in the database."""
    from app.core.security import get_password_hash
    from datetime import datetime, timezone

    user = User(
        email=test_admin_data["email"],
        phone_number=test_admin_data["phone_number"],
        first_name=test_admin_data["first_name"],
        last_name=test_admin_data["last_name"],
        hashed_password=get_password_hash(test_admin_data["password"]),
        role=test_admin_data["role"],
        is_active=True,
        is_2fa_enabled=False,
    )

    test_session.add(user)
    test_session.commit()
    test_session.refresh(user)

    # Create associated account with tokens
    from app.core.security import create_access_token, create_refresh_token
    from app.core.config import settings
    from datetime import timedelta

    access_token = create_access_token(data={"sub": user.email})
    refresh_token = create_refresh_token(data={"sub": user.email})

    account = Account(
        user_id=user.id,
        access_token=access_token,
        expires_at=datetime.now(timezone.utc) + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES),
        refresh_token=refresh_token,
        refresh_token_expires_at=datetime.now(timezone.utc) + timedelta(minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES),
        last_login=datetime.now(timezone.utc),
        last_failed_login_attempts=0,
    )

    test_session.add(account)
    test_session.commit()
    test_session.refresh(account)
    test_session.refresh(user)

    return user


@pytest.fixture(scope="function")
def auth_headers(test_user: User) -> dict:
    """Create authentication headers for test user."""
    from app.core.security import create_access_token

    access_token = create_access_token(data={"sub": test_user.email})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture(scope="function")
def admin_auth_headers(test_admin_user: User) -> dict:
    """Create authentication headers for test admin user."""
    from app.core.security import create_access_token

    access_token = create_access_token(data={"sub": test_admin_user.email})
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture(scope="function")
def mock_email_service():
    """Mock email service for testing."""
    mock_service = Mock()
    mock_service.send_email.return_value = True
    mock_service.send_template_email.return_value = True
    mock_service.send_otp_email.return_value = True
    mock_service.send_welcome_email.return_value = True
    mock_service.send_password_reset_email.return_value = True
    return mock_service


@pytest.fixture(scope="function")
def mock_otp_service():
    """Mock OTP service for testing."""
    mock_service = Mock()
    mock_service.generate_and_send_otp.return_value = {
        "message": "OTP sent successfully",
        "expires_in": 300,
        "delivery_method": "email",
        "purpose": "login",
    }
    mock_service.verify_otp.return_value = {
        "message": "OTP verified successfully",
        "verified": True,
        "purpose": "login",
    }
    return mock_service


# Test configuration overrides
@pytest.fixture(autouse=True)
def override_settings():
    """Override settings for testing."""
    original_values = {}

    # Override settings for testing
    test_overrides = {
        "TESTING": True,  # Enable testing mode
        "DATABASE_URL": TEST_DATABASE_URL,
        "REDIS_URL": "redis://localhost:6379/15",  # Use different DB for tests
        "SECRET_KEY": "test-secret-key-for-testing-only",
        "ACCESS_TOKEN_EXPIRE_MINUTES": 30,
        "REFRESH_TOKEN_EXPIRE_MINUTES": 2880,
        "OTP_EXPIRE_MINUTES": 5,
        "RATE_LIMIT_ENABLED": False,  # Disable rate limiting in tests
        "EMAIL_FROM": "<EMAIL>",
        "SMTP_HOST": "localhost",
        "SMTP_PORT": 587,
        "DEBUG": True,
    }

    # Store original values and apply overrides
    for key, value in test_overrides.items():
        if hasattr(settings, key):
            original_values[key] = getattr(settings, key)
            setattr(settings, key, value)

    yield

    # Restore original values
    for key, value in original_values.items():
        setattr(settings, key, value)
