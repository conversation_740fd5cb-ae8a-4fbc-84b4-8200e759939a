# tests/test_docker_integration.py
"""
Integration tests that use Docker services (PostgreSQL and Redis).
These tests require Docker services to be running.
"""

import pytest
import psycopg2
import redis
from sqlmodel import Session, create_engine
from fastapi.testclient import TestClient
from app.main import app
from app.db.database import get_session
from app.schemas.user_schema import User, UserRoleEnum
from app.core.security import get_password_hash
from app.core.config import settings


# Docker service connection strings
DOCKER_DATABASE_URL = settings.DATABASE_URL
DOCKER_REDIS_URL = settings.REDIS_URL


@pytest.fixture(scope="session")
def docker_postgres_available():
    """Check if Docker PostgreSQL is available."""
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="surabhi_db",
            user="surabhi_user",
            password="surabhi_password"
        )
        conn.close()
        return True
    except Exception:
        pytest.skip("Docker PostgreSQL not available")


@pytest.fixture(scope="session")
def docker_redis_available():
    """Check if Docker Redis is available."""
    try:
        r = redis.Redis(host="localhost", port=6379, password="redis_password")
        r.ping()
        return True
    except Exception:
        pytest.skip("Docker Redis not available")


@pytest.fixture(scope="function")
def docker_db_session(docker_postgres_available):
    """Create database session using Docker PostgreSQL."""
    engine = create_engine(DOCKER_DATABASE_URL)
    
    # Create tables
    from app.schemas.user_schema import SQLModel
    SQLModel.metadata.create_all(engine)
    
    with Session(engine) as session:
        yield session
    
    # Cleanup - drop tables
    SQLModel.metadata.drop_all(engine)


@pytest.fixture(scope="function")
def docker_redis_client(docker_redis_available):
    """Create Redis client using Docker Redis."""
    client = redis.Redis.from_url(DOCKER_REDIS_URL)
    
    # Clear database before test
    client.flushdb()
    
    yield client
    
    # Cleanup
    client.flushdb()
    client.close()


@pytest.fixture(scope="function")
def docker_client(docker_db_session):
    """Create test client with Docker database."""
    def get_docker_session():
        return docker_db_session
    
    app.dependency_overrides[get_session] = get_docker_session
    
    with TestClient(app) as test_client:
        yield test_client
    
    app.dependency_overrides.clear()


@pytest.mark.requires_docker
class TestDockerIntegration:
    """Integration tests using Docker services."""
    
    def test_database_connection(self, docker_db_session):
        """Test database connection with Docker PostgreSQL."""
        # Test basic database operations
        user = User(
            email="<EMAIL>",
            phone_number="+**********",
            first_name="Docker",
            last_name="Test",
            hashed_password=get_password_hash("TestPassword123!"),
            role=UserRoleEnum.EMPLOYEE,
            is_active=True,
            is_2fa_enabled=False
        )
        
        docker_db_session.add(user)
        docker_db_session.commit()
        docker_db_session.refresh(user)
        
        assert user.id is not None
        assert user.email == "<EMAIL>"
        
        # Test query
        from sqlmodel import select
        statement = select(User).where(User.email == "<EMAIL>")
        result = docker_db_session.exec(statement).first()
        
        assert result is not None
        assert result.email == "<EMAIL>"
    
    def test_redis_connection(self, docker_redis_client):
        """Test Redis connection with Docker Redis."""
        # Test basic Redis operations
        docker_redis_client.set("test_key", "test_value")
        value = docker_redis_client.get("test_key")
        assert value == "test_value"
        
        # Test expiration
        docker_redis_client.setex("temp_key", 1, "temp_value")
        assert docker_redis_client.get("temp_key") == "temp_value"
        
        # Test hash operations
        docker_redis_client.hset("test_hash", "field1", "value1")
        hash_value = docker_redis_client.hget("test_hash", "field1")
        assert hash_value == "value1"
    
    def test_api_with_docker_services(self, docker_client):
        """Test API endpoints with Docker services."""
        # Test health endpoint
        response = docker_client.get("/api/v1/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] in ["healthy", "degraded"]
        assert "services" in data
        assert "database" in data["services"]
        
        # Test user registration
        user_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "phone_number": "+**********",
            "first_name": "Docker",
            "last_name": "API"
        }
        
        response = docker_client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201
        
        data = response.json()
        assert "user" in data
        assert "access_token" in data
        assert data["user"]["email"] == user_data["email"]
        
        # Test login
        login_data = {
            "username": user_data["email"],
            "password": user_data["password"]
        }
        
        response = docker_client.post("/api/v1/auth/login", data=login_data)
        assert response.status_code == 200
        
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        
        # Test authenticated endpoint
        access_token = data["access_token"]
        headers = {"Authorization": f"Bearer {access_token}"}
        
        response = docker_client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == 200
        
        data = response.json()
        assert data["email"] == user_data["email"]
    
    def test_cache_integration(self, docker_redis_client):
        """Test cache service integration with Docker Redis."""
        from app.services.cache_service import CacheService
        import asyncio
        
        async def test_cache_operations():
            # Create cache service instance
            cache_service = CacheService()
            
            # Test set and get
            await cache_service.set("integration_test", {"data": "test_value"}, ttl=300)
            result = await cache_service.get("integration_test")
            assert result is not None
            assert result["data"] == "test_value"
            
            # Test delete
            await cache_service.delete("integration_test")
            result = await cache_service.get("integration_test")
            assert result is None
            
            # Test exists
            await cache_service.set("exists_test", "value", ttl=300)
            exists = await cache_service.exists("exists_test")
            assert exists is True
            
            not_exists = await cache_service.exists("non_existent_key")
            assert not_exists is False
        
        # Run async test
        asyncio.run(test_cache_operations())
    
    def test_performance_with_docker(self, docker_client):
        """Test performance with Docker services."""
        import time
        
        # Test multiple requests to ensure connection pooling works
        start_time = time.time()
        
        for i in range(10):
            response = docker_client.get("/api/v1/health")
            assert response.status_code == 200
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should complete 10 requests in reasonable time (less than 5 seconds)
        assert total_time < 5.0
        
        # Check that performance headers are present
        response = docker_client.get("/health")
        assert "X-Process-Time" in response.headers
        assert "X-Request-ID" in response.headers
    
    def test_database_transactions(self, docker_db_session):
        """Test database transaction handling."""
        from sqlmodel import select
        
        # Test successful transaction
        user1 = User(
            email="<EMAIL>",
            phone_number="+**********",
            first_name="Transaction",
            last_name="Test1",
            hashed_password=get_password_hash("TestPassword123!"),
            role=UserRoleEnum.EMPLOYEE,
            is_active=True,
            is_2fa_enabled=False
        )
        
        docker_db_session.add(user1)
        docker_db_session.commit()
        
        # Verify user was created
        statement = select(User).where(User.email == "<EMAIL>")
        result = docker_db_session.exec(statement).first()
        assert result is not None
        
        # Test rollback
        user2 = User(
            email="<EMAIL>",
            phone_number="+1234567892",
            first_name="Transaction",
            last_name="Test2",
            hashed_password=get_password_hash("TestPassword123!"),
            role=UserRoleEnum.EMPLOYEE,
            is_active=True,
            is_2fa_enabled=False
        )
        
        docker_db_session.add(user2)
        # Don't commit, rollback instead
        docker_db_session.rollback()
        
        # Verify user was not created
        statement = select(User).where(User.email == "<EMAIL>")
        result = docker_db_session.exec(statement).first()
        assert result is None


@pytest.mark.requires_docker
def test_docker_services_health():
    """Test that Docker services are healthy and accessible."""
    # Test PostgreSQL
    try:
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="surabhi_db",
            user="surabhi_user",
            password="surabhi_password",
            connect_timeout=5
        )
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        assert result[0] == 1
        cursor.close()
        conn.close()
    except Exception as e:
        pytest.fail(f"PostgreSQL health check failed: {e}")
    
    # Test Redis
    try:
        r = redis.Redis.from_url(DOCKER_REDIS_URL)
        pong = r.ping()
        assert pong is True
        r.close()
    except Exception as e:
        pytest.fail(f"Redis health check failed: {e}")


if __name__ == "__main__":
    # Run Docker integration tests
    pytest.main([__file__, "-v", "--tb=short"])
