# tests/performance/test_load.py
"""
Load testing for the Surabhi API.
Tests the system's ability to handle high concurrent loads.
"""

import asyncio
import time
import pytest
import aiohttp
from typing import List, Dict, Any


@pytest.mark.performance
@pytest.mark.slow
class TestLoadPerformance:
    """Load testing for API endpoints."""

    BASE_URL = "http://localhost:8000"

    async def make_request(
        self, session: aiohttp.ClientSession, endpoint: str
    ) -> Dict[str, Any]:
        """Make a single HTTP request and measure response time."""
        start_time = time.time()
        try:
            async with session.get(f"{self.BASE_URL}{endpoint}") as response:
                response_time = time.time() - start_time
                return {
                    "status": response.status,
                    "response_time": response_time,
                    "success": response.status < 400,
                }
        except Exception as e:
            return {
                "status": 0,
                "response_time": time.time() - start_time,
                "success": False,
                "error": str(e),
            }

    async def concurrent_requests(
        self, endpoint: str, num_requests: int
    ) -> List[Dict[str, Any]]:
        """Make multiple concurrent requests to an endpoint."""
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=50)
        timeout = aiohttp.ClientTimeout(total=30)

        async with aiohttp.ClientSession(
            connector=connector, timeout=timeout
        ) as session:
            tasks = [self.make_request(session, endpoint) for _ in range(num_requests)]
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Filter out exceptions and convert to results
            valid_results = []
            for result in results:
                if isinstance(result, dict):
                    valid_results.append(result)
                else:
                    valid_results.append(
                        {
                            "status": 0,
                            "response_time": 0,
                            "success": False,
                            "error": str(result),
                        }
                    )

            return valid_results

    @pytest.mark.asyncio
    async def test_health_endpoint_load(self):
        """Test health endpoint under load."""
        # Check if server is available
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.BASE_URL}/api/v1/health", timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status >= 400:
                        pytest.skip("Server not available for load testing")
        except Exception:
            pytest.skip("Server not available for load testing")

        num_requests = 100
        results = await self.concurrent_requests("/api/v1/health", num_requests)

        # Analyze results
        successful_requests = [r for r in results if r["success"]]
        failed_requests = [r for r in results if not r["success"]]

        success_rate = len(successful_requests) / len(results) * 100
        avg_response_time = (
            sum(r["response_time"] for r in successful_requests)
            / len(successful_requests)
            if successful_requests
            else 0
        )
        max_response_time = (
            max(r["response_time"] for r in successful_requests)
            if successful_requests
            else 0
        )

        # Assertions - More lenient for test environment
        assert success_rate >= 80, f"Success rate {success_rate}% is below 80%"
        assert avg_response_time < 30.0, (
            f"Average response time {avg_response_time}s exceeds 30 seconds"
        )
        assert max_response_time < 60.0, (
            f"Max response time {max_response_time}s exceeds 60 seconds"
        )

        print("\nLoad Test Results for /health:")
        print(f"  Total requests: {num_requests}")
        print(f"  Successful: {len(successful_requests)}")
        print(f"  Failed: {len(failed_requests)}")
        print(f"  Success rate: {success_rate:.2f}%")
        print(f"  Average response time: {avg_response_time:.3f}s")
        print(f"  Max response time: {max_response_time:.3f}s")

    @pytest.mark.asyncio
    async def test_docs_endpoint_load(self):
        """Test documentation endpoint under load."""
        num_requests = 50  # Lower for docs endpoint
        results = await self.concurrent_requests("/docs", num_requests)

        successful_requests = [r for r in results if r["success"]]
        success_rate = len(successful_requests) / len(results) * 100
        avg_response_time = (
            sum(r["response_time"] for r in successful_requests)
            / len(successful_requests)
            if successful_requests
            else 0
        )

        # More lenient for docs endpoint
        assert success_rate >= 90, f"Success rate {success_rate}% is below 90%"
        assert avg_response_time < 3.0, (
            f"Average response time {avg_response_time}s exceeds 3 seconds"
        )

        print("\nLoad Test Results for /docs:")
        print(f"  Total requests: {num_requests}")
        print(f"  Successful: {len(successful_requests)}")
        print(f"  Success rate: {success_rate:.2f}%")
        print(f"  Average response time: {avg_response_time:.3f}s")



@pytest.mark.performance
@pytest.mark.slow
class TestStressPerformance:
    """Stress testing to find system breaking points."""

    BASE_URL = "http://localhost:8000"

    @pytest.mark.asyncio
    async def test_gradual_load_increase(self):
        """Test system behavior under gradually increasing load."""
        load_levels = [300, 500, 800, 1000, 1500]
        results = {}

        for load in load_levels:
            print(f"\nTesting with {load} concurrent requests...")

            connector = aiohttp.TCPConnector(limit=load + 10, limit_per_host=load)
            timeout = aiohttp.ClientTimeout(total=60)

            async with aiohttp.ClientSession(
                connector=connector, timeout=timeout
            ) as session:
                start_time = time.time()
                tasks = [self.make_request(session, "/api/v1/health") for _ in range(load)]
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                total_time = time.time() - start_time

                successful = sum(
                    1
                    for r in responses
                    if isinstance(r, dict) and r.get("success", False)
                )
                success_rate = successful / load * 100
                throughput = load / total_time

                results[load] = {
                    "success_rate": success_rate,
                    "total_time": total_time,
                    "throughput": throughput,
                }

                print(f"  Success rate: {success_rate:.2f}%")
                print(f"  Total time: {total_time:.2f}s")
                print(f"  Throughput: {throughput:.2f} req/s")

                # Stop if success rate drops below 80%
                if success_rate < 80:
                    print(f"  Breaking point reached at {load} concurrent requests")
                    break

        # Verify that system handles at least 300 concurrent requests well
        assert results.get(300, {}).get("success_rate", 0) >= 95, (
            "System should handle 300 concurrent requests with 95% success rate"
        )

    async def make_request(
        self, session: aiohttp.ClientSession, endpoint: str
    ) -> Dict[str, Any]:
        """Make a single HTTP request and measure response time."""
        start_time = time.time()
        try:
            async with session.get(f"{self.BASE_URL}{endpoint}") as response:
                response_time = time.time() - start_time
                return {
                    "status": response.status,
                    "response_time": response_time,
                    "success": response.status < 400,
                }
        except Exception as e:
            return {
                "status": 0,
                "response_time": time.time() - start_time,
                "success": False,
                "error": str(e),
            }


if __name__ == "__main__":
    # Run performance tests
    pytest.main([__file__, "-v", "-s", "--tb=short"])
